{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@tallo/applications": ["libs/applications/src/index.ts"], "@tallo/assets": ["libs/assets/*"], "@tallo/auth": ["libs/auth/src/index.ts"], "@tallo/design-system": ["libs/design-system/src/index.ts"], "@tallo/file-manager": ["libs/file-manager/src/index.ts"], "@tallo/http": ["libs/http/src/index.ts"], "@tallo/investor/actions-needed": ["libs/investor/actions-needed/src/index.ts"], "@tallo/investor/activity-feed": ["libs/investor/activity-feed/src/index.ts"], "@tallo/investor/applications": ["libs/investor/applications/src/index.ts"], "@tallo/investor/availability": ["libs/investor/availability/src/index.ts"], "@tallo/investor/company": ["libs/investor/company/src/index.ts"], "@tallo/investor/conversation": ["libs/investor/conversation/src/index.ts"], "@tallo/investor/intelligent-escalations": ["libs/investor/intelligent-escalations/src/index.ts"], "@tallo/investor/investor": ["libs/investor/investor/src/index.ts"], "@tallo/investor/navigation": ["libs/investor/navigation/src/index.ts"], "@tallo/investor/portfolio-statistics": ["libs/investor/portfolio-statistics/src/index.ts"], "@tallo/investor/property": ["libs/investor/property/src/index.ts"], "@tallo/investor/renter": ["libs/investor/renter/src/index.ts"], "@tallo/investor/showing": ["libs/investor/showing/src/index.ts"], "@tallo/investor/showing-agents": ["libs/investor/showing-agents/src/index.ts"], "@tallo/navigation": ["libs/navigation/src/index.ts"], "@tallo/property": ["libs/property/src/index.ts"], "@tallo/renter": ["libs/renter/renter/src/index.ts"], "@tallo/renter/applications": ["libs/renter/applications/src/index.ts"], "@tallo/renter/navigation": ["libs/renter/navigation/src/index.ts"], "@tallo/renter/trans-union": ["libs/renter/trans-union/src/index.ts"], "@tallo/styles": ["libs/styles/src/index.ts"], "@tallo/user": ["libs/user/src/index.ts"], "@tallo/utility": ["libs/utility/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}