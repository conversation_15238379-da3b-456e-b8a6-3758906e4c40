{"name": "tallo-fe", "packageManager": "npm@9.6.4", "private": true, "scripts": {"start": "nx run-many -t serve", "build": "nx build", "lint": "nx run-many -t lint", "docs:build": "nx build docs", "docs:dev": "nx serve docs", "format": "prettier --write \"**/*.{ts,tsx,md,vue}\"", "prepare": "husky"}, "dependencies": {"@datadog/browser-rum": "^5.8.0", "@floating-ui/vue": "^1.1.6", "@googlemaps/js-api-loader": "^1.16.6", "@pinia/colada": "^0.13.3", "@stripe/stripe-js": "^4.1.0", "@vue/runtime-dom": "^3.5.13", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^12.8.2", "axios": "^1.7.4", "chart.js": "^4.5.0", "date-fns": "4.1.0", "driver.js": "1.3.6", "focus-trap": "^7.5.4", "maska": "^3.0.4", "normalize.css": "8.0.1", "pinia": "2.3.0", "pluralize": "^8.0.0", "socket.io-client": "^4.7.1", "sortablejs": "^1.15.2", "tslib": "^2.3.0", "uuid": "11.0.5", "vue": "3.5.18", "vue-chartjs": "^5.3.2", "vue-router": "4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.4.0", "@commitlint/config-conventional": "^19.2.2", "@datadog/datadog-ci": "^3.20.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "~9.9.0", "@nx/devkit": "21.4.1", "@nx/eslint": "21.4.1", "@nx/eslint-plugin": "21.4.1", "@nx/js": "21.4.1", "@nx/playwright": "21.4.1", "@nx/vite": "21.4.1", "@nx/vue": "21.4.1", "@nx/web": "21.4.1", "@nx/workspace": "21.4.1", "@playwright/test": "1.55.0", "@swc-node/register": "^1.8.0", "@swc/core": "^1.3.85", "@swc/helpers": "~0.5.11", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/google.accounts": "^0.0.14", "@types/google.maps": "^3.55.12", "@types/node": "22.12.0", "@types/pluralize": "^0.0.33", "@types/sortablejs": "^1.15.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-vue": "5.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/compiler-sfc": "^3.4.38", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.1", "eslint": "~8.57.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "^9.16.1", "husky": "^9.1.4", "jiti": "2.4.2", "jsdom": "~22.1.0", "lint-staged": "^15.2.9", "nx": "21.4.1", "prettier": "^3.4.2", "sass": "1.77.8", "ts-node": "10.9.1", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-dts": "4.5.4", "vite-plugin-static-copy": "3.1.2", "vite-svg-loader": "5.1.0", "vitepress": "1.6.4", "vitest": "^3.2.4", "vue-tsc": "3.0.6"}, "lint-staged": {"{apps,libs}/**/*.+(js|ts|vue)": ["eslint"], "*.{vue,js,jsx,ts,tsx,json,css,scss,md,yaml}": ["prettier --write"]}, "engines": {"npm": "^10.8.0", "node": "^22.0.0"}}