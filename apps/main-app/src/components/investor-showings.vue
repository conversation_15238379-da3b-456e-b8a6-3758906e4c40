<template>
  <PageContainer>
    <View gap="lg" direction="column">
      <View justify="space-between" align="center" :grow="1">
        <Text variant="h1">Showings</Text>

        <MenuButton :buttonLabel="selectedTimeFilter" size="medium">
          <MenuButtonItem
            v-for="filter in timeFilters"
            :key="filter"
            :title="filter"
            @click="onFilterSelected(filter)"
          />
        </MenuButton>
      </View>

      <View v-if="state.status === 'pending'" justify="center" padding="xl">
        <LoadingIndicator />
      </View>

      <EmptyPagePlaceholder
        v-else-if="state.status === 'error'"
        title="Could not load showings"
        description="Please reload the page or try again later"
      />

      <template v-else>
        <TopBarFilters :selectedFilter="selectedFilter" :filters="filters" @selected="filterShowings" />

        <EmptyPagePlaceholder
          v-if="filteredShowings.length === 0"
          :title="'You don’t have any showings yet'"
          :description="'<PERSON><PERSON> is working to schedule showings. Once there is activity, you can see everything related to showings here'"
        />

        <div v-else class="showings">
          <ShowingCard v-for="showing in filteredShowings" :key="showing.id" :showing="showing" />
        </div>
      </template>
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  TopBarFilters,
  EmptyPagePlaceholder,
  Card,
  PageContainer,
  Text,
  View,
  MenuButtonItem,
  MenuButton,
  LoadingIndicator,
} from '@tallo/design-system';
import { ShowingFilters, ShowingCard, useFilteredShowings } from '@tallo/investor/showing';
import { TimeFilter, timeFilters } from '@tallo/utility';

const filters = Object.values(ShowingFilters);

const router = useRouter();
const route = useRoute();

const { state, filteredShowings } = useFilteredShowings();
const selectedFilter = computed(() => route.query.filter as ShowingFilters);

const selectedTimeFilter = computed({
  get: () => (route.query.timeFilter as TimeFilter) || TimeFilter.LAST_30_DAYS,
  set: (value: TimeFilter) => updateQueryFilters({ timeFilter: value }),
});

function onFilterSelected(filter: TimeFilter) {
  selectedTimeFilter.value = filter;
}

function filterShowings(filter: ShowingFilters) {
  updateQueryFilters({ filter });
}

function updateQueryFilters(params: { timeFilter?: TimeFilter; filter?: ShowingFilters }) {
  router.replace({
    query: {
      ...route.query,
      ...params,
    },
  });
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.showings {
  display: grid;
  gap: var(--t-spacing-sm);
  grid-template-columns: repeat(auto-fill, minmax(23.5rem, 1fr));

  @include screen-max-sm {
    grid-template-columns: 1fr;
  }
}
</style>
