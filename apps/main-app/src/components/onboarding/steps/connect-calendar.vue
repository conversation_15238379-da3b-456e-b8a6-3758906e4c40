<template>
  <InvestorOnboardingStep>
    <View direction="column" gap="xl" align="start">
      <Text variant="h2">Connect your calendar (optional)</Text>
      <Text variant="body-medium" color="secondary">
        Sync availability and let Tallo auto-schedule showings. You can skip for now.
      </Text>

      <Button
        v-if="!isConnected"
        variant="primary"
        size="medium"
        :loading="isConnecting"
        :disabled="isConnecting"
        @click="connectCalendar"
      >
        Connect Google Calendar
      </Button>

      <Button
        v-else
        variant="secondary"
        size="medium"
        :loading="isConnecting"
        :disabled="isConnecting"
        @click="disconnect"
      >
        Disconnect Google Calendar
      </Button>

      <View direction="row" gap="xs" class="nav-buttons">
        <Transition name="btn-fade">
          <Button variant="ghost" @click="back" :disabled="typing.value || isConnecting">Back</Button>
        </Transition>
        <Transition name="btn-fade">
          <Button variant="ghost" @click="skip" :disabled="typing.value || isConnecting">Skip</Button>
        </Transition>
      </View>
    </View>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { CalendarProvider, googleCalendarService } from '@tallo/auth';
import { Button, Text, View, useAlertsStore } from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { AppRouteNames } from '@tallo/investor/navigation';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { useOnboardingStore } from '../store/onboarding.store';

const router = useRouter();
const store = useOnboardingStore();
const investorStore = useInvestorStore();
const typing = inject('chatTyping', { value: false }) as { value: boolean };
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;

const nav = inject('onboardingNav') as any;
const alerts = useAlertsStore();

const isConnected = computed(() => {
  const googleCalendar = investorStore.investor?.user?.calendars?.find(
    (calendar) => calendar.provider === CalendarProvider.GOOGLE,
  );
  return !!googleCalendar;
});
const isConnecting = ref(false);
const oauthWindow = ref<Window | null>(null);
const windowCheckInterval = ref<number | null>(null);

const canContinue = computed(() => !typing.value && !isConnecting.value);

onMounted(() => {
  window.addEventListener('message', handleMessage);
});
onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
  if (windowCheckInterval.value) {
    clearInterval(windowCheckInterval.value);
  }
});

// Register/unregister continue action (skip) based on whether it's available
watch(
  canContinue,
  (canContinue) => {
    registerContinueAction(canContinue ? skip : null);
  },
  { immediate: true },
);

async function handleMessage(event: MessageEvent) {
  if (event.origin !== window.location.origin) return;
  if (event.data?.type !== 'GOOGLE_CALENDAR_CONNECTED') return;
  if (oauthWindow.value && event.source !== oauthWindow.value) return;

  resetConnectionState();

  if (event.data.success) {
    await investorStore.load();
    alerts.success('Google Calendar connected successfully');
    onConnected();
  } else {
    alerts.error('Failed to connect Google Calendar');
  }

  // Remove listener after handling once
  window.removeEventListener('message', handleMessage);
}

function resetConnectionState() {
  isConnecting.value = false;
  oauthWindow.value = null;
  if (windowCheckInterval.value) {
    clearInterval(windowCheckInterval.value);
    windowCheckInterval.value = null;
  }
}

function onConnected() {
  store.calendarConnected = true;
  store.calendarStepCompleted = true;
  router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION });
}

function skip() {
  store.calendarConnected = false;
  store.calendarStepCompleted = true; // mark the step done even if skipped
  router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION });
}
function back() {
  nav?.goBack?.();
}

async function disconnect() {
  if (isConnecting.value) return; // Prevent double-clicking

  isConnecting.value = true;
  try {
    await googleCalendarService.disconnect(CalendarProvider.GOOGLE);
    await investorStore.load();
    alerts.success('Google Calendar disconnected');
  } catch {
    alerts.error('Failed to disconnect Google Calendar');
  } finally {
    isConnecting.value = false;
  }
}

async function connectCalendar() {
  if (isConnecting.value) return; // Prevent double-clicking

  isConnecting.value = true;
  try {
    const authUrl = await googleCalendarService.connect();
    const url = new URL(authUrl);
    url.searchParams.append('isPrimary', 'true');

    // Open OAuth flow directly; callback page will postMessage back to us
    // Note: Do NOT use 'noopener'/'noreferrer' here because we rely on window.opener.postMessage from the callback window.
    oauthWindow.value = window.open(url.toString(), '_blank');
  } catch (error) {
    console.error('Failed to initiate Google Calendar connection:', error);
    alerts.error('Failed to initiate Google Calendar connection');
    resetConnectionState();
  }
}
</script>

<style scoped>
.nav-buttons {
  margin-left: auto;
}
</style>
