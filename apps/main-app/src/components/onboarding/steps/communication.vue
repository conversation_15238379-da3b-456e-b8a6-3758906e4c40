<template>
  <InvestorOnboardingStep>
    <form @submit.prevent="next">
      <FormGroup>
        <Text variant="h2">How do you want <PERSON><PERSON> to send you updates?</Text>

        <RadioButtonGroup direction="column">
          <RadioCard name="channel" :value="PreferredCommunicationChannel.Email" v-model="channel">
            <View direction="row" gap="sm" align="center">
              <IconTile icon="mail-01" size="small" color="violet" :coloredIcon="true" />
              <View direction="column" gap="xs">
                <Text variant="label-medium">Email</Text>
                <Text variant="body-medium" color="secondary">Get detailed updates delivered to your inbox.</Text>
              </View>
            </View>
          </RadioCard>
          <RadioCard name="channel" :value="PreferredCommunicationChannel.Sms" v-model="channel">
            <View direction="row" gap="sm" align="center">
              <IconTile icon="phone" size="small" color="orange" :coloredIcon="true" />
              <View direction="column" gap="xs">
                <Text variant="label-medium">SMS</Text>
                <Text variant="body-medium" color="secondary">Short and instant updates to your phone.</Text>
              </View>
            </View>
          </RadioCard>
        </RadioButtonGroup>

        <div class="phone-wrap">
          <Transition name="fade-down">
            <FormField
              label="Phone number"
              :error="form.fieldErrors('phoneNumber')"
              v-show="channel === PreferredCommunicationChannel.Sms"
            >
              <InputPhone v-model="form.fields.phoneNumber" />
            </FormField>
          </Transition>
        </div>

        <View direction="row" justify="end" gap="sm">
          <Transition name="btn-fade">
            <Button variant="ghost" type="button" @click="back">Back</Button>
          </Transition>
          <Transition name="btn-fade">
            <Button type="submit" :disabled="typing.value || !isFormValid">Continue</Button>
          </Transition>
        </View>
      </FormGroup>
    </form>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { requiredIf } from '@vuelidate/validators';
import { computed, inject, watch } from 'vue';
import { useRouter } from 'vue-router';

import {
  Button,
  FormField,
  FormGroup,
  IconTile,
  InputPhone,
  RadioButtonGroup,
  RadioCard,
  Text,
  View,
  useForm,
  phoneNumberValidator,
} from '@tallo/design-system';
import { AppRouteNames } from '@tallo/investor/navigation';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { PreferredCommunicationChannel, useOnboardingStore } from '../store/onboarding.store';

const store = useOnboardingStore();
const router = useRouter();
const typing = inject('chatTyping', { value: false }) as { value: boolean };
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;
const channel = computed({
  get: () => store.preferredCommunicationChannel,
  set: (v) => (store.preferredCommunicationChannel = v),
});
const nav = inject('onboardingNav') as any;

const form = useForm<{ phoneNumber: string }>(
  { phoneNumber: store.phoneNumber },
  {
    phoneNumber: computed(() => ({
      requiredIf: requiredIf(() => channel.value === PreferredCommunicationChannel.Sms),
      phoneNumberValidator,
    })),
  },
);

const isFormValid = computed(() => {
  if (!channel.value) return false;
  if (channel.value === PreferredCommunicationChannel.Sms) {
    return !!form.fields.phoneNumber && form.fieldErrors('phoneNumber').length === 0;
  }
  return true;
});

const canContinue = computed(() => isFormValid.value && !typing.value);

function next() {
  store.phoneNumber = form.fields.phoneNumber;
  router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_FINISH });
}
function back() {
  nav?.goBack?.();
}

// Register/unregister continue action based on whether button is active
watch(
  canContinue,
  (canContinue) => {
    registerContinueAction(canContinue ? next : null);
  },
  { immediate: true },
);
</script>
.fade-down-enter-active, .fade-down-leave-active { transition: opacity .18s ease, transform .18s ease; }
.fade-down-enter-from, .fade-down-leave-to { opacity: 0; transform: translateY(-4px); }

<style scoped>
.phone-wrap {
  min-height: 86px;
  display: grid;
  align-items: start;
}
</style>
