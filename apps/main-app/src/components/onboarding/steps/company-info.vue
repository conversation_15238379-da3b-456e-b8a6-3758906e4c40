<template>
  <InvestorOnboardingStep>
    <form @submit.prevent="next">
      <FormGroup>
        <FormField label="Company Name" :error="form.fieldErrors('companyName')">
          <Input v-model="form.fields.companyName" placeholder="Acme Property Group" />
        </FormField>

        <FormField label="Team size" :error="form.fieldErrors('numberOfEmployees')">
          <Select v-model="form.fields.numberOfEmployees">
            <option disabled value="">Select team size</option>
            <option v-for="opt in companyNumberOfEmployeesOptions" :key="opt" :value="opt">
              {{ opt }}
            </option>
          </Select>
        </FormField>

        <FormField label="How many units do you oversee?" :error="form.fieldErrors('numberOfUnitsOverseeing')">
          <Input type="number" v-model="form.fields.numberOfUnitsOverseeing" placeholder="e.g. 42" />
        </FormField>

        <FormField label="What would you like <PERSON><PERSON> to help you with?">
          <Textarea maxlength="200" v-model="form.fields.perceivedProductValueProposition" />
        </FormField>

        <View justify="end" gap="sm">
          <Transition name="btn-fade">
            <Button variant="ghost" type="button" @click="back">Back</Button>
          </Transition>
          <Transition name="btn-fade">
            <Button type="submit" :disabled="typing.value || !isFormValid">Continue</Button>
          </Transition>
        </View>
      </FormGroup>
    </form>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { minValue, helpers, required } from '@vuelidate/validators';
import { computed, inject, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Button, FormField, FormGroup, Input, Select, Textarea, View, useForm } from '@tallo/design-system';
import { CompanyNumberOfEmployees, companyNumberOfEmployeesOptions } from '@tallo/investor/company';
import { AppRouteNames } from '@tallo/investor/navigation';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { useOnboardingStore } from '../store/onboarding.store';

const store = useOnboardingStore();
const router = useRouter();
const typing = inject('chatTyping', { value: false }) as { value: boolean };
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;

const companyNameValidator = helpers.withMessage(
  'Please enter a valid company name (not only numbers)',
  (value: string) => {
    if (!value) return false;
    return !/^\d+$/.test(value);
  },
);

const form = useForm<{
  companyName: string;
  numberOfEmployees: CompanyNumberOfEmployees | '';
  numberOfUnitsOverseeing: number | null;
  perceivedProductValueProposition: string;
}>(
  {
    companyName: store.companyName,
    numberOfEmployees: (store.numberOfEmployees as CompanyNumberOfEmployees) || '',
    numberOfUnitsOverseeing: store.numberOfUnitsOverseeing,
    perceivedProductValueProposition: store.perceivedProductValueProposition,
  },
  {
    companyName: { required, companyName: companyNameValidator },
    numberOfUnitsOverseeing: { minValue: minValue(1) },
  },
);

function next() {
  form.submit(() => {
    store.companyName = form.fields.companyName;
    store.numberOfEmployees = form.fields.numberOfEmployees || null;
    store.numberOfUnitsOverseeing = form.fields.numberOfUnitsOverseeing;
    store.perceivedProductValueProposition = form.fields.perceivedProductValueProposition;

    router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY });
  });
}

const isFormValid = computed(() => {
  return (
    !!form.fields.companyName && !!form.fields.numberOfEmployees && (form.fields.numberOfUnitsOverseeing ?? 0) >= 1
  );
});

const canContinue = computed(() => isFormValid.value && !typing.value);

function back() {
  const nav = inject('onboardingNav') as any;
  nav?.goBack?.();
}

// Register/unregister continue action based on whether button is active
watch(
  canContinue,
  (canContinue) => {
    registerContinueAction(canContinue ? next : null);
  },
  { immediate: true },
);
</script>
