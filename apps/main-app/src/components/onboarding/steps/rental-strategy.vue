<template>
  <InvestorOnboardingStep>
    <View direction="column" gap="lg">
      <Text variant="h2">How strict should screening be?</Text>

      <RadioButtonGroup direction="column">
        <RadioCard name="screeningSensitivity" :value="ScreeningSensitivity.STRICT" v-model="model">
          <View direction="row" gap="sm" align="center">
            <IconTile icon="user-check-02" size="small" color="green" />
            <View direction="column" gap="xs">
              <Text variant="label-medium">Strict</Text>
              <Text variant="body-medium" color="secondary">
                Only renters who meet every requirement can book a showing. Highest quality, fewer showing requests.
              </Text>
            </View>
          </View>
        </RadioCard>
        <RadioCard name="screeningSensitivity" :value="ScreeningSensitivity.MODERATE" v-model="model">
          <View direction="row" gap="sm" align="center">
            <IconTile icon="users-01" size="small" color="yellow" />
            <View direction="column" gap="xs">
              <Text variant="label-medium">Moderate</Text>
              <Text variant="body-medium" color="secondary">
                Allow reasonable compromises and still keep standards. Balanced quality and showing volume.
              </Text>
            </View>
          </View>
        </RadioCard>
        <RadioCard name="screeningSensitivity" :value="ScreeningSensitivity.OWNER_REVIEW" v-model="model">
          <View direction="row" gap="sm" align="center">
            <IconTile icon="users-plus" size="small" color="blue" />
            <View direction="column" gap="xs">
              <Text variant="label-medium">Owner review</Text>
              <Text variant="body-medium" color="secondary">
                No automatic rejections. Most leads possible, but you’ll manually review showing requests.
              </Text>
            </View>
          </View>
        </RadioCard>
      </RadioButtonGroup>

      <View justify="end" gap="sm">
        <Transition name="btn-fade">
          <Button variant="ghost" @click="back">Back</Button>
        </Transition>
        <Transition name="btn-fade">
          <Button :disabled="!model || typing.value" @click="next">Continue</Button>
        </Transition>
      </View>
    </View>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { computed, inject, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Button, RadioButtonGroup, RadioCard, Text, View, IconTile } from '@tallo/design-system';
import { AppRouteNames } from '@tallo/investor/navigation';
import { ScreeningSensitivity } from '@tallo/property';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { useOnboardingStore } from '../store/onboarding.store';

const store = useOnboardingStore();
const router = useRouter();
const typing = inject('chatTyping', { value: false }) as { value: boolean };
const nav = inject('onboardingNav') as any;
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;

const model = computed({ get: () => store.screeningSensitivity, set: (v) => (store.screeningSensitivity = v) });
const canContinue = computed(() => !!model.value && !typing.value);

function next() {
  router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_CONNECT_CALENDAR });
}
function back() {
  nav?.goBack?.();
}

watch(
  canContinue,
  (canContinue) => {
    registerContinueAction(canContinue ? next : null);
  },
  { immediate: true },
);
</script>
