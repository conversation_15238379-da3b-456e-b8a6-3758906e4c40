<template>
  <InvestorOnboardingStep :card-class="'panel celebration'">
    <View direction="column" align="center" gap="xl" class="content column">
      <img class="hero" src="/house.png" alt="" aria-hidden="true" />

      <View direction="column" gap="lg" align="center" class="copy">
        <View direction="column" gap="sm" align="center">
          <Text variant="h2">You’re all set!</Text>
          <Text variant="body-large" color="secondary" align="center" style="max-width: 25rem">
            Let’s add your first property and kick off your Tallo journey
          </Text>
        </View>

        <View padding-bottom="xl">
          <Button size="medium" @click="addProperty">Add first property</Button>
        </View>
      </View>
    </View>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { inject, onMounted } from 'vue';

import { Button, Text, View } from '@tallo/design-system';
import { useNavigation } from '@tallo/investor/navigation';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { useOnboardingStore } from '../store/onboarding.store';

const { openNewPropertyOnboarding } = useNavigation();
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;

function addProperty() {
  setTimeout(() => openNewPropertyOnboarding(), 120);
}

// Register the add property action as the continue action
onMounted(() => {
  registerContinueAction(addProperty);
});
</script>

<style scoped>
.content {
  width: 100%;
  padding-left: var(--t-spacing-md);
  padding-right: var(--t-spacing-md);
}
.hero {
  width: clamp(120px, 20vw, 250px);
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 12px 28px rgba(0, 0, 0, 0.12));
}
.copy {
  max-width: 620px;
}
</style>
