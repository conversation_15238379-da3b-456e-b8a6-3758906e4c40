<template>
  <InvestorOnboardingStep>
    <View direction="column" gap="lg">
      <Text variant="h2">Choose your role</Text>
      <RadioButtonGroup direction="column">
        <RadioCard name="accountType" :value="AccountType.Company" v-model="model">
          <View direction="row" gap="sm" align="center">
            <IconTile icon="building-06" size="small" color="yellow" />
            <View direction="column" gap="xs">
              <Text variant="label-medium">Company</Text>
              <Text variant="body-small" color="secondary">
                Invite teammates, standardize workflows, and scale your leasing ops.
              </Text>
            </View>
          </View>
        </RadioCard>
        <RadioCard name="accountType" :value="AccountType.Individual" v-model="model">
          <View direction="row" gap="sm" align="center">
            <IconTile icon="user-01" size="small" color="neutral" />
            <View direction="column" gap="xs">
              <Text variant="label-medium">Individual Owner</Text>
              <Text variant="body-small" color="secondary">
                Simplify showings and decisions with an AI copilot built for speed.
              </Text>
            </View>
          </View>
        </RadioCard>
      </RadioButtonGroup>

      <View justify="end" gap="sm">
        <Transition name="btn-fade">
          <Button variant="ghost" @click="back">Back</Button>
        </Transition>
        <Transition name="btn-fade">
          <Button :disabled="!model || typing.value" @click="next">Continue</Button>
        </Transition>
      </View>
    </View>
  </InvestorOnboardingStep>
</template>

<script setup lang="ts">
import { computed, inject, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Button, RadioButtonGroup, RadioCard, Text, View, IconTile } from '@tallo/design-system';
import { AppRouteNames } from '@tallo/investor/navigation';

import InvestorOnboardingStep from '../components/investor-onboarding-step.vue';
import { AccountType, useOnboardingStore } from '../store/onboarding.store';

const router = useRouter();
const store = useOnboardingStore();
const typing = inject('chatTyping', { value: false }) as { value: boolean };
const registerContinueAction = inject('registerContinueAction') as (action: (() => void) | null) => void;

const model = computed({
  get: () => store.accountType,
  set: (v) => (store.accountType = v),
});

const canContinue = computed(() => !!model.value && !typing.value);

function next() {
  if (store.accountType === AccountType.Company) {
    router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_COMPANY_INFO });
  } else {
    router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY });
  }
}
function back() {
  const nav = inject('onboardingNav') as any;
  nav?.goBack?.();
}

// Register/unregister continue action based on whether button is active
watch(
  canContinue,
  (canContinue) => {
    registerContinueAction(canContinue ? next : null);
  },
  { immediate: true },
);
</script>
