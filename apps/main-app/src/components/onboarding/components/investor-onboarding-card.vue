<template>
  <Card variant="secondary" :height="'auto'" :class="['onboarding-card', cardClass]">
    <div class="onboarding-card-header">
      <slot name="header"></slot>
    </div>
    <div class="onboarding-card-body">
      <slot name="body"></slot>
    </div>
    <div class="onboarding-card-footer">
      <slot name="footer"></slot>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { Card } from '@tallo/design-system';

withDefaults(defineProps<{ cardClass?: string }>(), { cardClass: '' });
</script>

<style scoped>
.onboarding-card {
  width: 100%;
}

.onboarding-card-body {
  width: 100%;
}
</style>
