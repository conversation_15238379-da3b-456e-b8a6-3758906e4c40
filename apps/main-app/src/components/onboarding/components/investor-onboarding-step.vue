<template>
  <InvestorOnboardingCard :card-class="cardClass">
    <template #header>
      <slot name="header"></slot>
    </template>

    <template #body>
      <div class="content" :class="{ 'full-width': fullWidth }">
        <Text v-if="title" variant="h2" class="title">{{ title }}</Text>
        <div class="form">
          <slot></slot>
        </div>
      </div>
    </template>

    <template #footer>
      <slot name="footer"></slot>
    </template>
  </InvestorOnboardingCard>
</template>

<script setup lang="ts">
import { Text } from '@tallo/design-system';

import InvestorOnboardingCard from './investor-onboarding-card.vue';

withDefaults(defineProps<{ title?: string; fullWidth?: boolean; cardClass?: string }>(), {
  fullWidth: false,
  cardClass: '',
});
</script>

<style scoped>
.content {
  width: 100%;
}
.form {
  width: 100%;
}
.title {
  margin-bottom: var(--t-spacing-md);
}
</style>
