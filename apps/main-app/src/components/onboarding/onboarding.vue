<template>
  <div class="onboarding onboarding-theme">
    <PageContainer>
      <div class="grid">
        <!-- Left: Assistant header + typed step text -->
        <div class="left">
          <div class="top-row">
            <div class="assistant-row">
              <Icon name="sparkle" size="small" class="assistant-icon" />
              <Text class="assistant-label" variant="label-medium">Tallo Assistant</Text>
            </div>
          </div>

          <div class="typed" aria-live="polite">
            <Text v-for="(line, i) in typedLines" :key="i" variant="h3" class="typed-line">{{ line }}</Text>
            <Text v-if="typedText" variant="h3" class="typed-line">{{ typedText }}</Text>

            <Transition name="btn-fade">
              <div v-if="isWelcome && !typing" class="welcome-cta">
                <Button @click="start">Get started</Button>
              </div>
            </Transition>
          </div>
        </div>

        <!-- Right: Step indicator row + Form, consistent slide-in from right -->
        <div class="right">
          <Transition name="fade-right">
            <div v-if="!isWelcome && stepIndex >= 0" v-show="!typing" class="right-step-row">
              <Text variant="label-small" color="secondary">Step {{ stepIndex + 1 }} of {{ totalSteps }}</Text>
              <div class="dot-row">
                <button
                  v-for="(s, i) in stepperSteps"
                  :key="s.name"
                  class="step-dot"
                  :class="{ active: i === stepIndex, done: i < stepIndex, locked: !isStepAllowed(i) }"
                  :aria-current="i === stepIndex ? 'step' : undefined"
                  :disabled="!isStepAllowed(i)"
                  @click="goToStep(i)"
                ></button>
              </div>
            </div>
          </Transition>
          <Transition name="fade-right">
            <div v-show="!typing" :key="route.fullPath">
              <RouterView />
            </div>
          </Transition>
        </div>
      </div>
    </PageContainer>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, provide, computed, onBeforeUnmount, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Button, Icon, PageContainer, Text } from '@tallo/design-system';
import { useCompanyStore } from '@tallo/investor/company';
import type { InvestorOnboardingSignUpDto } from '@tallo/investor/investor';
import { useInvestorStore, investorOnboardingService } from '@tallo/investor/investor';
import { AppRouteNames } from '@tallo/investor/navigation';
import { ScreeningSensitivity } from '@tallo/property';

import { PreferredCommunicationChannel, useOnboardingStore } from './store/onboarding.store';

const route = useRoute();
const router = useRouter();
const investorStore = useInvestorStore();

const store = useOnboardingStore();

const typedLines = ref<string[]>([]);
const typedText = ref('');
const typing = ref(false);
let typingRunId = 0;

// Ref to track if continue button should be triggered on Enter
const continueAction = ref<(() => void) | null>(null);

// Expose typing state to steps to optionally gate actions
provide('chatTyping', typing);

const isWelcome = computed(() => route.name === AppRouteNames.INVESTOR_ONBOARDING_WELCOME);

const steps = [
  { name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME },
  { name: AppRouteNames.INVESTOR_ONBOARDING_ACCOUNT_TYPE },
  { name: AppRouteNames.INVESTOR_ONBOARDING_COMPANY_INFO },
  { name: AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY },
  { name: AppRouteNames.INVESTOR_ONBOARDING_CONNECT_CALENDAR },
  { name: AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION },
  { name: AppRouteNames.INVESTOR_ONBOARDING_FINISH },
];
const stepperSteps = steps.filter((s) => s.name !== AppRouteNames.INVESTOR_ONBOARDING_WELCOME);
const stepIndex = computed(() => stepperSteps.findIndex((s) => s.name === route.name));
const totalSteps = stepperSteps.length;

function isStepAllowed(targetIndex: number) {
  // Locking rules: you can only go forward to the next incomplete step
  // Allowed if targetIndex <= current completed frontier
  const completed = new Set<string>();
  // Account type
  if (store.accountType) completed.add(AppRouteNames.INVESTOR_ONBOARDING_ACCOUNT_TYPE);
  // Company info (if company account type)
  if (store.accountType) {
    const companyDone =
      store.accountType === 'Individual' ||
      (!!store.companyName && !!store.numberOfEmployees && store.numberOfUnitsOverseeing !== null);
    if (companyDone) completed.add(AppRouteNames.INVESTOR_ONBOARDING_COMPANY_INFO);
  }
  // Strategy
  if (store.screeningSensitivity) completed.add(AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY);
  // Calendar step can be completed by connecting or skipping
  if (store.calendarStepCompleted) completed.add(AppRouteNames.INVESTOR_ONBOARDING_CONNECT_CALENDAR);
  // Communication
  if (store.preferredCommunicationChannel && (store.preferredCommunicationChannel !== 'Sms' || !!store.phoneNumber)) {
    completed.add(AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION);
  }

  const order = stepperSteps.map((s) => s.name);
  const highestCompletedIdx = Math.max(-1, ...order.map((n, i) => (completed.has(n) ? i : -1)));
  const allowedMax = highestCompletedIdx + 1; // next step after last completed
  return targetIndex <= allowedMax;
}

function goToStep(index: number) {
  if (index < 0 || index >= stepperSteps.length) return;
  if (!isStepAllowed(index)) return;
  router.push({ name: stepperSteps[index].name as any });
}

function goBack() {
  if (stepIndex.value <= 0) router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME });
  else goToStep(stepIndex.value - 1);
}

// Keyboard event handler for Enter key
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && continueAction.value && !typing.value) {
    // Prevent default form submission if we're handling it
    event.preventDefault();
    continueAction.value();
  }
}

// Guard against direct navigation to disallowed steps
watch(
  () => route.name,
  (name) => {
    const idx = stepperSteps.findIndex((s) => s.name === name);
    if (idx >= 0 && !isStepAllowed(idx)) {
      // find first allowed index
      let redirectIndex = 0;
      for (let i = 0; i < stepperSteps.length; i++) {
        if (!isStepAllowed(i)) break;
        redirectIndex = i;
      }
      router.replace({ name: stepperSteps[redirectIndex].name as any });
    }
  },
  { immediate: true },
);

// Initial route guard: if arriving at onboarding root or unknown, go to Welcome
if (!route.name || route.name === AppRouteNames.INVESTOR_ONBOARDING || route.name === ('' as any)) {
  router.replace({ name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME });
}

provide('onboardingNav', { goToStep, goBack, stepIndex, stepperSteps, isStepAllowed });
provide('registerContinueAction', (action: (() => void) | null) => {
  continueAction.value = action;
});

onMounted(() => {
  if (!store.started) {
    store.start();
    router.replace({ name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME });
  }
  // Hide right panel immediately to avoid blink before typing kicks in
  typingRunId++;
  typing.value = true;
  typedLines.value = [];
  typedText.value = '';
  typeForRoute(route.name as string);

  // Add keyboard event listener
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  // Remove keyboard event listener and reset store
  document.removeEventListener('keydown', handleKeydown);
  store.reset();
});

onBeforeUnmount(() => {
  typingRunId++;
});

watch(
  () => route.name,
  (name) => {
    if (!store.started && name !== AppRouteNames.INVESTOR_ONBOARDING_WELCOME) {
      router.replace({ name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME });
      return;
    }
    // Immediately hide right panel to prevent blink before typing starts
    typingRunId++; // invalidate previous typing run
    typing.value = true;
    typedLines.value = [];
    typedText.value = '';
    typeForRoute(name as string);
  },
);

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function waitFor(predicate: () => boolean, timeoutMs = 1500, intervalMs = 40) {
  const start = Date.now();
  while (!predicate()) {
    if (Date.now() - start > timeoutMs) break;
    await sleep(intervalMs);
  }
}

async function typeOutLines(lines: string[]) {
  const runId = ++typingRunId;
  typing.value = true;
  typedLines.value = [];
  typedText.value = '';
  for (const line of lines) {
    if (typingRunId !== runId) return; // cancelled
    await typeCharacters(line, runId);
    if (typingRunId !== runId) return; // cancelled
    typedLines.value.push(typedText.value);
    typedText.value = '';
    await sleep(150);
  }
  if (typingRunId !== runId) return;
  typing.value = false;
}

async function typeCharacters(line: string, runId: number) {
  typedText.value = '';
  for (let i = 0; i < line.length; i++) {
    if (typingRunId !== runId) return;
    typedText.value += line[i];
    await sleep(18 + Math.random() * 18);
  }
}

async function typeForRoute(name: string) {
  // Wait briefly for investor to load, so we can greet by name
  await waitFor(() => !!investorStore.investor?.user?.name, 1500, 50);

  const userName = investorStore.investor?.user?.name?.split(' ')[0] || 'there';
  const connectedCalendar = store.calendarConnected;
  const channel = store.preferredCommunicationChannel;

  const copyMap: Record<string, string[]> = {
    [AppRouteNames.INVESTOR_ONBOARDING_WELCOME]: [
      `Welcome, ${userName}! I’m Tallo, your leasing assistant.`,
      `Just a few quick steps and you’ll unlock scheduling, renter profiles, and faster decisions.`,
    ],
    [AppRouteNames.INVESTOR_ONBOARDING_ACCOUNT_TYPE]: [
      `First things first — are you an individual owner or a company?`,
      `I’ll tailor the setup around how you work.`,
    ],
    [AppRouteNames.INVESTOR_ONBOARDING_COMPANY_INFO]: [
      `Great — tell me a bit about your company, and I’ll set things up so everything runs smoothly.`,
    ],
    [AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY]: [
      `Next, how strict should screening be before someone can book a showing?`,
      `You can go strict, keep it balanced, or review every single request yourself.`,
    ],
    [AppRouteNames.INVESTOR_ONBOARDING_CONNECT_CALENDAR]: connectedCalendar
      ? ['Nice — calendar connected! I’ll keep your availability in sync so scheduling stays smooth.']
      : [
          store.screeningSensitivity === ScreeningSensitivity.STRICT
            ? 'I’ll be selective so only qualified renters can book showings.'
            : store.screeningSensitivity === ScreeningSensitivity.MODERATE
              ? 'I’ll balance standards with momentum to keep showings moving.'
              : 'I’ll open the funnel wide and let you review showing requests manually.',
          'Want to connect your calendar so I can schedule without the back-and-forth? You can skip this for now.',
        ],
    [AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION]: connectedCalendar
      ? [
          'Great — your calendar’s set.',
          channel == null
            ? 'Now, how should I keep you updated: text or email?'
            : channel === PreferredCommunicationChannel.Sms
              ? 'Texts it is. Short, timely nudges without the noise.'
              : 'Email it is. Clean, relevant updates when it matters.',
        ]
      : [
          'No problem. You can connect a calendar later in your profile.',
          'Now, how should I keep you updated: text or email?',
        ],
    [AppRouteNames.INVESTOR_ONBOARDING_FINISH]: [
      'All set — nice work!',
      'When you’re ready, add your first property to kick things off.',
    ],
  };
  await typeOutLines(copyMap[name] || []);
}

function start() {
  store.start();
  router.push({ name: AppRouteNames.INVESTOR_ONBOARDING_ACCOUNT_TYPE });
}

// Watch for welcome step to register start action
watch(
  isWelcome,
  (isWelcome) => {
    if (isWelcome && !typing.value) {
      continueAction.value = start;
    } else if (isWelcome) {
      continueAction.value = null;
    }
  },
  { immediate: true },
);

// Also watch typing state for welcome step
watch(typing, (typing) => {
  if (isWelcome.value) {
    continueAction.value = typing ? null : start;
  }
});

const companyStore = useCompanyStore();
let savedOnboarding = false;

watch(
  () => route.name,
  async (name) => {
    if (name === AppRouteNames.INVESTOR_ONBOARDING_FINISH && !savedOnboarding) {
      try {
        await companyStore.load();
        const companyId = companyStore.company?.id;
        if (!companyId) return;

        const payload: InvestorOnboardingSignUpDto = {
          phoneNumber:
            store.preferredCommunicationChannel === 'Sms' && store.phoneNumber ? store.phoneNumber : undefined,
          companyName: store.companyName || undefined,
          numberOfEmployees: (store.numberOfEmployees as any) || undefined,
          numberOfUnitsOverseeing: store.numberOfUnitsOverseeing ?? undefined,
          perceivedProductValueProposition: store.perceivedProductValueProposition || undefined,
          accountType: store.accountType || undefined,
          preferredCommunicationChannel: store.preferredCommunicationChannel || undefined,
          screeningSensitivity: store.screeningSensitivity ?? undefined,
        };

        await investorOnboardingService.fillCompanySignUpInfo(companyId, payload);
        savedOnboarding = true;
      } catch (e) {
        console.error('Failed to save onboarding info', e);
      }
    }
  },
);
</script>

<style scoped lang="scss">
.onboarding {
  height: 100%;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 50/50 */
  align-items: start;
  gap: var(--t-spacing-xl);
  padding: var(--t-spacing-xl) var(--t-spacing-lg);
}

.left {
  display: flex;
  flex-direction: column;
  gap: var(--t-spacing-xl);
}
.top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.assistant-row {
  display: flex;
  align-items: center;
  gap: var(--t-spacing-xs);
}
.assistant-icon {
  color: #fe6723;
}
.assistant-label {
  color: var(--t-color-neutral-700);
}
.step-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.left-column {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--t-spacing-lg);
  align-items: start;
}
.vstepper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-top: 6px;
}
.v-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--t-color-neutral-300);
}
.v-circle.active {
  background: var(--t-color-neutral-800);
}
.v-circle.done {
  background: var(--t-color-neutral-600);
}
.right .dot-row .step-dot.locked {
  background: transparent;
  box-shadow: none;
  border: 1px dashed var(--t-color-neutral-300);
}

.typed {
  display: flex;
  flex-direction: column;
  gap: var(--t-spacing-md);
  margin-top: 0;
  min-height: 6rem;
}
.caret {
  width: 1px;
  height: 1.5rem;
  background: var(--t-color-neutral-700);
  animation: blink 1s step-end infinite;
  margin-top: 4px;
}
@keyframes blink {
  50% {
    opacity: 0;
  }
}

.right {
  min-height: 50vh;
}
.right-step-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: var(--t-spacing-lg);
}
.back-btn {
  margin-right: auto;
}
.dot-row {
  display: inline-flex;
  gap: 8px;
}
/* Screenshot-like stepper: light orange for done, solid for active, gray for future */
.step-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  background: var(--t-color-neutral-300);
}
.step-dot.done {
  background: rgba(254, 103, 35, 0.35);
}
.step-dot.active {
  background: #fe6723;
  box-shadow: 0 0 0 4px rgba(254, 103, 35, 0.14);
}

.chat {
  display: flex;
  flex-direction: column;
  gap: var(--t-spacing-md);
}
.bubble {
  max-width: 40rem;
  padding: var(--t-spacing-lg);
  border-radius: var(--t-border-radius-md);
  background: var(--t-color-neutral-100);
}
.bubble.user {
  align-self: flex-end;
  background: var(--t-color-neutral-white);
  border: 1px solid var(--t-color-neutral-300);
}

.typing {
  display: flex;
  gap: 6px;
  padding: 8px 0;
}
.typing .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--t-color-neutral-400);
  animation: bounce 1s infinite ease-in-out;
}
.typing .dot:nth-child(2) {
  animation-delay: 0.15s;
}
.typing .dot:nth-child(3) {
  animation-delay: 0.3s;
}
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Consistent subtle fade in from right to left for the right panel */
.fade-right-enter-active {
  transition: all 220ms ease;
}
.fade-right-leave-active {
  transition: all 150ms ease;
}
.fade-right-enter-from {
  opacity: 0;
  transform: translateX(16px);
}
.fade-right-leave-to {
  opacity: 0;
  transform: translateX(8px);
}

/* Buttons: fade in, instant hide (no ghosting) */
.btn-fade-enter-active {
  transition: opacity 160ms ease;
}
.btn-fade-leave-active {
  transition: none;
}
.btn-fade-enter-from {
  opacity: 0;
}
.btn-fade-leave-to {
  opacity: 0;
}
.welcome-cta {
  margin-top: var(--t-spacing-md);
}

/* Step dots near label for minimal indicator (matches screenshot) */
.step-dot {
  display: inline-block;
  width: 6px;
  height: 12px;
  border-radius: 50%;
  background: #fe6723;
}

/* Reserve enough space for typing on small screens so stepper/form don't jump */
@media (max-width: 768px) {
  .typed {
    min-height: 10rem;
  }
}
@media (max-width: 520px) {
  .typed {
    min-height: 12.5rem;
  }
}

.typed {
  padding-left: var(--t-spacing-sm);
  padding-right: var(--t-spacing-sm);
}
.right {
  padding-left: var(--t-spacing-sm);
  padding-right: var(--t-spacing-sm);
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
    padding-left: var(--t-spacing-md);
    padding-right: var(--t-spacing-md);
  }
}
</style>
