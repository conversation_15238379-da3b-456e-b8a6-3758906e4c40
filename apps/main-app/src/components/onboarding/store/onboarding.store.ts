import { acceptHMRUpdate, defineStore } from 'pinia';

import { ScreeningSensitivity } from '@tallo/property';

export enum AccountType {
  Company = 'Company',
  Individual = 'Individual',
}

export enum PreferredCommunicationChannel {
  Email = 'Email',
  Sms = 'Sms',
}

interface OnboardingState {
  started: boolean;
  accountType: AccountType | null;

  companyName: string;
  numberOfEmployees: string | null; // use enum value string from investor/company options
  numberOfUnitsOverseeing: number | null;
  perceivedProductValueProposition: string;
  screeningSensitivity: ScreeningSensitivity | null;
  calendarConnected: boolean;
  calendarStepCompleted: boolean; // either connected or explicitly skipped
  preferredCommunicationChannel: PreferredCommunicationChannel | null;
  phoneNumber: string;
}

export const useOnboardingStore = defineStore({
  id: 'investorOnboarding',
  state: (): OnboardingState => ({
    started: false,
    accountType: null,
    companyName: '',
    numberOfEmployees: null,
    numberOfUnitsOverseeing: null,
    perceivedProductValueProposition: '',
    screeningSensitivity: null,
    calendarConnected: false,
    calendarStepCompleted: false,
    preferredCommunicationChannel: null,
    phoneNumber: '',
  }),
  getters: {
    isCompany(state): boolean {
      return state.accountType === AccountType.Company;
    },
  },
  actions: {
    start() {
      this.started = true;
    },
    reset() {
      this.$reset();
    },
  },
});

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useOnboardingStore, import.meta.hot));
}
