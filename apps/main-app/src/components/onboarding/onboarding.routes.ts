import { RouteRecordRaw } from 'vue-router';

import { AppRouteNames, AppRoutes } from '@tallo/investor/navigation';

import OnboardingLayout from './onboarding.vue';
import AccountType from './steps/account-type.vue';
import Communication from './steps/communication.vue';
import CompanyInfo from './steps/company-info.vue';
import ConnectCalendar from './steps/connect-calendar.vue';
import Finish from './steps/finish.vue';
import RentalStrategy from './steps/rental-strategy.vue';
import Welcome from './steps/welcome.vue';

export const investorOnboardingRoutes: RouteRecordRaw = {
  name: AppRouteNames.INVESTOR_ONBOARDING,
  path: AppRoutes.INVESTOR_ONBOARDING,
  component: OnboardingLayout,
  meta: { public: false },
  children: [
    { path: '', name: AppRouteNames.INVESTOR_ONBOARDING_WELCOME, component: Welcome },
    { path: 'account-type', name: AppRouteNames.INVESTOR_ONBOARDING_ACCOUNT_TYPE, component: AccountType },
    { path: 'company', name: AppRouteNames.INVESTOR_ONBOARDING_COMPANY_INFO, component: CompanyInfo },
    { path: 'strategy', name: AppRouteNames.INVESTOR_ONBOARDING_RENTAL_STRATEGY, component: RentalStrategy },
    { path: 'calendar', name: AppRouteNames.INVESTOR_ONBOARDING_CONNECT_CALENDAR, component: ConnectCalendar },
    { path: 'communication', name: AppRouteNames.INVESTOR_ONBOARDING_COMMUNICATION, component: Communication },
    { path: 'finish', name: AppRouteNames.INVESTOR_ONBOARDING_FINISH, component: Finish },
  ],
};
