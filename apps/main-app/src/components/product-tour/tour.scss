/* Product tour (Driver.js) styles aligned with design system */
@keyframes t-tour-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.t-tour.driver-popover {
  position: fixed; /* keep positioning stable */
  display: flex;
  flex-direction: column;
  width: 95%;
  max-width: 450px;
  border-radius: var(--t-border-radius-md);
  background-color: var(--t-color-neutral-200);
  color: var(--t-text-color-primary);
  box-shadow: var(--t-shadow-md);
  z-index: 100000;
}

.driver-fade .t-tour.driver-popover {
  animation: t-tour-fade-in 200ms ease-in-out both;
}

/* Arrow triangles */
.t-tour.driver-popover .driver-popover-arrow {
  border-width: 6px !important;
  border-style: solid !important;
}
.t-tour .driver-popover-arrow-side-top {
  border-color: var(--t-color-neutral-200) transparent transparent transparent !important;
}
.t-tour .driver-popover-arrow-side-bottom {
  border-color: transparent transparent var(--t-color-neutral-200) transparent !important;
}
.t-tour .driver-popover-arrow-side-left {
  border-color: transparent transparent transparent var(--t-color-neutral-200) !important;
}
.t-tour .driver-popover-arrow-side-right {
  border-color: transparent var(--t-color-neutral-200) transparent transparent !important;
}
/* Hide arrow when using `over` mode */
.t-tour .driver-popover-arrow-side-over.driver-popover-arrow {
  display: none !important;
  border-width: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

/* Assistant header placed above the title */
.t-tour .driver-popover-title {
  font: var(--t-typography-heading-h4-font);
  order: 2;
}
.t-tour .driver-popover-description {
  margin-top: var(--t-spacing-lg);
  font: var(--t-typography-body-md-font);
  color: var(--t-color-neutral-700);
}

.t-tour .t-tour-assistant {
  order: 1;
  display: flex;
  align-items: center;
  gap: var(--t-spacing-xs);
  margin-bottom: var(--t-spacing-md);
  font: var(--t-typography-heading-h6-font);
  color: var(--t-color-neutral-800);
  letter-spacing: 0.16px;
}
.t-tour .t-tour-assistant .avatar {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.t-tour .t-tour-assistant .avatar svg {
  display: block;
  width: 16px;
  height: 16px;
  color: #fe6723;
}
.t-tour .t-tour-assistant .name {
  color: var(--t-color-neutral-900);
}

/* Headline/body helpers (if used) */
.t-tour .t-tour-title {
  font-weight: 600;
  font-size: var(--t-font-size-350);
  margin-bottom: var(--t-spacing-sm);
}
.t-tour .t-tour-body {
  font-size: var(--t-font-size-300);
}

/* Footer & buttons */
.t-tour .driver-popover-footer {
  margin-top: var(--t-spacing-xl);
}
.t-tour .driver-popover-navigation-btns {
  gap: 0.5rem;
}

/* Base button style to mimic DS Button small */
.t-tour .driver-popover-footer button {
  all: unset;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 1.75rem;
  padding: 0 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  font: var(--t-typography-heading-h6-font);
  line-height: normal;
  white-space: nowrap;
  border-radius: var(--t-border-radius-xs);
  cursor: pointer;
  transition: background-color 150ms ease-in-out, color 150ms ease-in-out, border-color 150ms ease-in-out;
}

/* Back button — ghost */
.t-tour .driver-popover-navigation-btns button:first-child {
  background-color: var(--t-color-neutral-white);
  color: var(--t-text-color-primary);
  border: 1px solid var(--t-color-neutral-300);
}
.t-tour .driver-popover-navigation-btns button:first-child:hover {
  background-color: var(--t-color-neutral-100);
}

/* Next/Finish — primary */
.t-tour .driver-popover-navigation-btns button:last-child {
  background-color: var(--t-color-neutral-800);
  color: var(--t-color-neutral-100);
}
.t-tour .driver-popover-navigation-btns button:last-child:hover {
  background-color: var(--t-color-neutral-900);
}

/* Close (X) */
.t-tour .driver-popover-close-btn {
  top: 6px;
  right: 6px;
  display: block;
}
.t-tour .driver-popover-close-btn:hover,
.t-tour .driver-popover-close-btn:focus {
  color: var(--t-text-color-primary);
}
