import { driver, Driver, type PopoverDOM, type Config } from 'driver.js';
import 'driver.js/dist/driver.css';

import { useInvestorStore, investorOnboardingService } from '@tallo/investor/investor';

export interface TourConfig extends Partial<Config> {
  onboardingType: 'property' | 'dashboard' | 'renterProfile';
  steps: Config['steps'];
}

class ProductTourService {
  private propertyTourStarted = false;
  private dashboardTourStarted = false;

  tryStartPropertyTour(propertyId: string) {
    if (!propertyId) return;
    const investorStore = useInvestorStore();
    const completed = investorStore.investor?.onboarding?.propertyOnboardingCompleted;

    if (completed !== false) return;

    if (this.propertyTourStarted) return;

    this.propertyTourStarted = true;
    this.startPropertyTour();
  }

  tryStartDashboardTour(propertyId: string) {
    if (!propertyId) return;
    const investorStore = useInvestorStore();
    const completed = investorStore.investor?.onboarding?.dashboardOnboardingCompleted;

    if (completed !== false) return;

    if (this.dashboardTourStarted) return;

    this.startDashboardTour();
  }

  tryStartRenterProfileTour() {
    const investorStore = useInvestorStore();
    const completed = investorStore.investor?.onboarding?.renterProfileOnboardingCompleted;
    if (completed === false) {
      this.startRenterProfileTour();
    }
  }

  private startRenterProfileTour(): Driver {
    const config: TourConfig = {
      onboardingType: 'renterProfile',
      stagePadding: 6,
      steps: [
        {
          popover: {
            title: 'Hi there!',
            description:
              'Congrats on getting your first lead. Let me show you how to review it and take the next step.',
            showButtons: ['next', 'close'],
          },
        },
        {
          element: '#rp-showing-datetime',
          popover: {
            title: 'Requested time',
            description:
              "Here's the date and time they'd like to tour. If it works, Accept. If not, you can Reschedule or Decline.",
            side: 'bottom',
            align: 'start',
          },
        },
        {
          element: '#rp-renter-highlights',
          popover: {
            title: 'Renter details',
            description: 'A quick snapshot of things like income, credit, and timeline — handy to decide fast.',
            side: 'top',
            align: 'start',
          },
        },
        {
          element: '#rp-inquiry-card',
          popover: {
            title: 'Application',
            description: "Ready to move forward? Send the application and I'll guide them through it.",
            side: 'top',
            align: 'end',
            doneBtnText: 'Finish',
            onNextClick: async (element, step, options) => {
              try {
                await investorOnboardingService.completeRenterProfileOnboarding();
              } finally {
                options.config.onDestroyed?.(element, step, options);
              }
            },
          },
        },
      ],
    };

    return this.createTour(config);
  }

  /**
   * Gets default driver configuration that all tours share
   */
  getDefaultConfig(): Partial<Config> {
    return {
      showProgress: true,
      allowClose: true,
      overlayClickBehavior: 'nextStep',
      smoothScroll: true,
      animate: true,
      disableActiveInteraction: true,
      stagePadding: 14,
      stageRadius: 8,
      popoverClass: 't-tour',
      prevBtnText: 'Back',
      nextBtnText: 'Next',
      doneBtnText: 'Finish',
      onPopoverRender: (popover) => this.ensureAssistantHeader(popover),
    };
  }

  /**
   * Creates a completion handler for the given onboarding type
   */
  createCompletionHandler(onboardingType: TourConfig['onboardingType']) {
    let completionReported = false;

    return async () => {
      if (completionReported) return;
      completionReported = true;

      try {
        switch (onboardingType) {
          case 'property':
            await investorOnboardingService.completePropertyOnboarding();
            break;
          case 'dashboard':
            await investorOnboardingService.completeDashboardOnboarding();
            break;
          case 'renterProfile':
            await investorOnboardingService.completeRenterProfileOnboarding();
            break;
        }

        // Refresh investor data to get updated onboarding status
        const investorStore = useInvestorStore();
        await investorStore.load();
      } catch (e) {
        console.error(`Failed to complete ${onboardingType} onboarding`, e);
      }
    };
  }

  /**
   * Creates and starts a tour with the given configuration
   */
  createTour(config: TourConfig): Driver {
    const completionHandler = this.createCompletionHandler(config.onboardingType);

    const driverConfig: Config = {
      ...this.getDefaultConfig(),
      ...config,
      onDestroyed: async () => {
        await completionHandler();
      },
    };

    const tour = driver(driverConfig);
    tour.drive();
    return tour;
  }

  private ensureAssistantHeader(popover: PopoverDOM) {
    const container = popover.wrapper;
    const titleEl = popover.title;
    if (!container || !titleEl) return;

    let header = container.querySelector('.t-tour-assistant') as HTMLElement | null;
    if (!header) {
      header = document.createElement('div');
      header.className = 't-tour-assistant';
      header.innerHTML =
        "<span class='avatar' aria-hidden='true' style='display:inline-flex;align-items:center;justify-content:center'>" +
        "<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' style='color:#fe6723;display:inline-block'>" +
        "<path d='M4.5 6V1M2 3.5H7M13 3L11.2658 7.50886C10.9838 8.24209 10.8428 8.60871 10.6235 8.91709C10.4292 9.1904 10.1904 9.42919 9.91709 9.62353C9.60871 9.8428 9.24209 9.98381 8.50886 10.2658L4 12L8.50886 13.7342C9.24209 14.0162 9.60871 14.1572 9.91709 14.3765C10.1904 14.5708 10.4292 14.8096 10.6235 15.0829C10.8428 15.3913 10.9838 15.7579 11.2658 16.4911L13 21L14.7342 16.4911C15.0162 15.7579 15.1572 15.3913 15.3765 15.0829C15.5708 14.8096 15.8096 14.5708 16.0829 14.3765C16.3913 14.1572 16.7579 14.0162 17.4911 13.7342L22 12L17.4911 10.2658C16.7579 9.98381 16.3913 9.8428 16.0829 9.62353C15.8096 9.42919 15.5708 9.1904 15.3765 8.91709C15.1572 8.60871 15.0162 8.24209 14.7342 7.50886L13 3Z' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>" +
        "</svg></span><span class='name'>Tallo Assistant</span>";
    }

    if (header.parentElement !== container || header.nextSibling !== titleEl) {
      container.insertBefore(header, titleEl);
    }
  }

  private startPropertyTour(): Driver {
    const config: TourConfig = {
      onboardingType: 'property',
      overlayOpacity: 0.5,
      steps: [
        {
          popover: {
            title: 'Congratulations!',
            description: `You’ve added your first property 🎉 Let me give you a quick tour so you know what's next.`,
            showButtons: ['next', 'close'],
          },
        },
        {
          element: '#prop-nav-details',
          popover: {
            title: 'Property details',
            description: `Your are on the property details page. When someone is interested in your property, I’ll use the info you gave me to answer to their questions. If I can't answer, I’ll check with you.`,
            side: 'bottom',
            align: 'start',
          },
        },
        {
          element: '#prop-parking-card',
          popover: {
            title: 'Edit property details',
            description:
              'You can edit property info anytime — things like parking, pet policy, or renter requirements. I’ll update your listing everywhere automatically.',
            side: 'top',
            align: 'start',
          },
        },
        {
          element: '#prop-nav-availability',
          popover: {
            title: 'Availability',
            description:
              'This is where I learn your availability. I’ll use it to pre-book showings for you, but you can always step in and propose a different time.',
            side: 'top',
            align: 'start',
          },
        },
        {
          onDeselected: async () => {
            try {
              await investorOnboardingService.completePropertyOnboarding();
            } catch (e) {
              console.error('Failed to complete property onboarding', e);
            }
          },
          element: '#prop-list-btn',
          popover: {
            title: 'List property',
            description: `That’s it for the basics! When you’re ready, hit <strong>List Property</strong>, and I’ll kick off the process.`,
            side: 'left',
            align: 'center',
            doneBtnText: 'Finish',
            onNextClick: async (element, step, options) => {
              try {
                await investorOnboardingService.completePropertyOnboarding();
              } finally {
                options.config.onDestroyed?.(element, step, options);
              }
            },
          },
        },
      ],
    };

    return this.createTour(config);
  }

  private startDashboardTour(): Driver {
    const config: TourConfig = {
      onboardingType: 'dashboard',
      overlayOpacity: 0.8,
      steps: [
        {
          popover: {
            title: 'Nice work!',
            description: `I have started syndicating your property. I will let you know when I got first leads coming in. In a mean time, let me show you around your property dashboard.`,
            showButtons: ['next', 'close'],
          },
        },
        {
          element: '#pd-stats',
          popover: {
            title: 'Track your performance',
            description:
              'Views, inquiries, and showing requests update in real time so you can see momentum at a glance.',
            side: 'bottom',
            align: 'start',
          },
        },
        {
          element: '#pd-auto-update',
          popover: {
            title: 'Smart updates',
            description: 'Enable auto‑updates and I’ll do my best to keep your listing top‑ranked.',
            side: 'top',
            align: 'start',
          },
        },
        {
          element: '#pd-listing-settings',
          popover: {
            title: 'Listing settings',
            description:
              'Adjust price, screening sensitivity, and tour options here. I’ll apply changes everywhere instantly.',
            side: 'top',
            align: 'end',
          },
        },
        {
          element: '#pd-showing-requests',
          popover: {
            title: 'Showing requests',
            description: `As soon as I qualify a lead, I will hand them over to you to make a decision if you want to move forward with them.`,
            side: 'top',
            align: 'start',
          },
        },
        {
          element: '#pd-escalations',
          popover: {
            title: 'Intelligent escalations',
            description:
              'If something needs your attention — like a property question, or renter running late — I’ll flag it here with a suggested next step.',
            side: 'top',
            align: 'start',
          },
        },
        {
          element: '#pd-upcoming-showings',
          popover: {
            title: 'Upcoming showings',
            description:
              'Here’s everything scheduled — easy to scan and manage. I’ll keep you posted if anything changes.',
            side: 'top',
            align: 'center',
            doneBtnText: 'Finish',
            onNextClick: async (element, step, options) => {
              try {
                await investorOnboardingService.completeDashboardOnboarding();
              } finally {
                options.config.onDestroyed?.(element, step, options);
              }
            },
          },
        },
      ],
    };

    return this.createTour(config);
  }
}

export default new ProductTourService();
