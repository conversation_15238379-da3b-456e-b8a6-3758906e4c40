<template>
  <PageContainer>
    <View gap="md" direction="column">
      <div>
        <View direction="column" gap="xs">
          <Text variant="h1">Availability</Text>
          <Text variant="body-medium" color="secondary" align="start">
            Set up the property availability and <PERSON><PERSON> will only propose showings during the times you are available
          </Text>
        </View>
      </div>

      <View wrap="wrap" padding-top="md">
        <PropertyAvailability />
      </View>
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { Card, PageContainer, Text, View } from '@tallo/design-system';

import PropertyAvailability from './property-availability.vue';
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';
</style>
