<template>
  <View v-if="availabilityStore.availabilityLoading" justify="center" padding="xl">
    <LoadingIndicator />
  </View>

  <View v-else direction="column" gap="xl" class="container">
    <View gap="sm" direction="column">
      <Text variant="h4">Showing duration</Text>

      <Select v-model="showingDuration" @change="durationChanged">
        <option value="10">10 minutes</option>
        <option value="15">15 minutes</option>
        <option value="30">30 minutes</option>
        <option value="45">45 minutes</option>
        <option value="60">1 hour</option>
      </Select>
    </View>

    <View gap="sm" direction="column" v-if="availabilityStore.availabilityLoaded">
      <Text variant="h4">Weekly schedule</Text>

      <Availability
        @slot-added="handleSlotAdded"
        @slot-updated="handleSlotUpdated"
        @slot-deleted="handleSlotDeleted"
        :availability="availabilityStore.getAvailabilitySlots"
        :investor-availability-id="availabilityStore.getInvestorAvailabilityId!"
        ref="availabilityRef"
      />
    </View>
  </View>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';

import { Card, LoadingIndicator, Select, Text, View } from '@tallo/design-system';
import { useAvailabilityStore, AvailabilitySlot, NewAvailabilitySlotDto } from '@tallo/investor/availability';
import { usePropertyStore } from '@tallo/investor/property';

import Availability from './availability.vue';

const availabilityStore = useAvailabilityStore();
const propertyStore = usePropertyStore();
const availabilityRef = ref<InstanceType<typeof Availability> | null>(null);

// Load availability when property becomes available
const loadAvailabilityIfNeeded = () => {
  const propertyId = propertyStore.property?.id;
  if (propertyId && (!availabilityStore.availabilityLoaded || availabilityStore.getCurrentPropertyId !== propertyId)) {
    availabilityStore.loadAvailability(propertyId);
  }
};

// Load on mount
onMounted(loadAvailabilityIfNeeded);

// Also watch for property changes (for when property loads after component mounts)
watch(
  () => propertyStore.property?.id,
  (propertyId) => {
    if (propertyId) {
      loadAvailabilityIfNeeded();
    }
  },
  { immediate: true },
);

const showingDuration = ref<number | null>(null);
let previousShowingDuration = ref<number | null>(null);

// Handle slot added event
const handleSlotAdded = async (newSlot: NewAvailabilitySlotDto) => {
  // Call the store method to add the slot and get the saved slot with ID
  const savedSlot = await availabilityStore.addSlot(newSlot, availabilityStore.getInvestorAvailabilityId!);

  // If we got a saved slot back, update the component
  if (savedSlot && availabilityRef.value) {
    availabilityRef.value.onSlotAdded(newSlot, savedSlot);
  }
};

// Handle slot updated event
const handleSlotUpdated = async (updatedSlot: AvailabilitySlot) => {
  // Call the store method to update the slot and get the saved slot
  const savedSlot = await availabilityStore.updateSlot(updatedSlot);

  // If we got a saved slot back, update the component
  if (savedSlot && availabilityRef.value) {
    availabilityRef.value.onSlotUpdated(savedSlot);
  }
};

const handleSlotDeleted = async (slotIds: string[]) => {
  await availabilityStore.deleteSlots(slotIds);

  if (availabilityRef.value) {
    availabilityRef.value.onSlotsDeleted(slotIds);
  }
};

const durationChanged = async () => {
  if (showingDuration.value) {
    try {
      await availabilityStore.updateShowingDuration(+showingDuration.value).then(() => {
        previousShowingDuration.value = showingDuration.value!;
      });
    } catch (error) {
      showingDuration.value = previousShowingDuration.value;
    }
  }
};

const { investorAvailability } = storeToRefs(availabilityStore);
watch(
  investorAvailability,
  (investorAvailabilityData) => {
    if (investorAvailabilityData) {
      showingDuration.value = investorAvailabilityData.showingDurationInMinutes;
      previousShowingDuration.value = investorAvailabilityData.showingDurationInMinutes;
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  max-width: 35rem;
}
</style>
