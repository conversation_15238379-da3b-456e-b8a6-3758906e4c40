<template>
  <div v-if="availability" class="availability">
    <div v-for="day in daysOfTheWeek" :key="day" class="container">
      <Label>
        <AvailabilitySwitchLegacy
          :model-value="hasSlotsForDay(day)"
          @update:modelValue="toggleDay(day, $event)"
          :disabled="hasPendingSlotsForDay(day)"
        />
        <span>{{ day }}</span>
      </Label>

      <div v-for="(slot, index) in toggledDays[day]" :key="slot.id || `pending-${day}-${index}`" class="time-slot">
        <div class="controls">
          <input
            class="time-input"
            type="time"
            v-model="slot.startTime"
            @change="handleValueChange(day, index)"
            :disabled="isPendingSlotWithoutId(day, index)"
          />
          <span>-</span>
          <input
            class="time-input"
            type="time"
            v-model="slot.endTime"
            @change="handleValueChange(day, index)"
            :disabled="isPendingSlotWithoutId(day, index)"
          />
          <IconButton
            variant="ghost"
            size="medium"
            :icon="index === 0 ? 'plus' : 'trash'"
            :aria-label="index === 0 ? 'add' : 'remove'"
            @click="index === 0 ? addTimeSlot(day) : removeTimeSlot(day, index)"
            :disabled="isPendingSlotWithoutId(day, index)"
          />
        </div>
        <p v-if="slotErrors?.[day]?.[index]" class="error-text">{{ slotErrors?.[day]?.[index] }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';

import { IconButton, Label } from '@tallo/design-system';
import { AvailabilitySlot, NewAvailabilitySlotDto } from '@tallo/investor/availability';
import { WeekDay } from '@tallo/utility';

import AvailabilitySwitchLegacy from './availability-switch-legacy.vue';

type SlotsByDay = Record<WeekDay, (Omit<AvailabilitySlot, 'id'> & { id?: string })[]>;
type PendingSlotData = {
  startTime: string;
  endTime: string;
};
type PendingSlots = Record<WeekDay, Map<number, PendingSlotData>>;

const props = defineProps<{
  availability: AvailabilitySlot[];
  investorAvailabilityId: string;
}>();

const daysOfTheWeek = Object.values(WeekDay);
const emit = defineEmits(['slot-added', 'slot-deleted', 'slot-updated']);
let toggledDays = reactive(initializeSlotsFromData(props.availability));
const slotErrors = reactive(initializeEmptySlots());
const pendingSlots = reactive<PendingSlots>({
  [WeekDay.SUNDAY]: new Map(),
  [WeekDay.MONDAY]: new Map(),
  [WeekDay.TUESDAY]: new Map(),
  [WeekDay.WEDNESDAY]: new Map(),
  [WeekDay.THURSDAY]: new Map(),
  [WeekDay.FRIDAY]: new Map(),
  [WeekDay.SATURDAY]: new Map(),
});

// Track locally deleted slot IDs to prevent them from being re-added by the watch function
const locallyDeletedSlotIds = reactive(new Set<string>());

// Track pending update operations to know when it's safe to clean up deleted slot IDs
const pendingUpdateOperations = reactive(new Set<string>());

watch(
  props.availability,
  (newAvailability) => {
    // Initialize slots from the new availability data, but filter out locally deleted slots
    const filteredAvailability = newAvailability.filter((slot) => !locallyDeletedSlotIds.has(slot.id));
    const slots = initializeSlotsFromData(filteredAvailability);

    // Update the slots while preserving user input
    Object.values(WeekDay).forEach((day) => {
      // Store current values and pending state before updating
      const currentSlots = [...toggledDays[day]];
      const pendingIndices = new Set(pendingSlots[day].keys());

      // Instead of completely replacing, merge the server data with local state
      const serverSlots = slots[day];
      const localSlots = currentSlots;

      // Create a map of existing local slots by ID for quick lookup
      const localSlotsById = new Map(localSlots.map((slot, index) => [slot.id, { slot, index }]));

      // Start with server slots, but preserve local modifications
      const mergedSlots = serverSlots.map((serverSlot) => {
        const localSlotData = localSlotsById.get(serverSlot.id);

        if (localSlotData && pendingIndices.has(localSlotData.index)) {
          // Preserve user input for pending slots
          return {
            ...serverSlot,
            startTime: localSlotData.slot.startTime,
            endTime: localSlotData.slot.endTime,
          };
        }

        return serverSlot;
      });

      // Add any local-only slots (new slots that haven't been saved yet)
      localSlots.forEach((localSlot) => {
        if (!localSlot.id) {
          // Check if this local slot matches any server slot by content
          const matchingServerSlot = serverSlots.find(
            (serverSlot) =>
              serverSlot.weekday === localSlot.weekday &&
              serverSlot.startTime === localSlot.startTime &&
              serverSlot.endTime === localSlot.endTime,
          );

          // Only add if there's no matching server slot
          if (!matchingServerSlot) {
            mergedSlots.push(localSlot);
          }
        }
      });

      // Update the day's slots
      toggledDays[day] = mergedSlots;

      // Update pending slots indices to match the new array
      const newPendingSlots = new Map<number, PendingSlotData>();
      mergedSlots.forEach((slot, newIndex) => {
        if (slot.id) {
          const localSlotData = localSlotsById.get(slot.id);
          if (localSlotData && pendingIndices.has(localSlotData.index)) {
            const pendingData = pendingSlots[day].get(localSlotData.index);
            if (pendingData) {
              newPendingSlots.set(newIndex, pendingData);
            }
          }
        } else {
          // For slots without IDs, find them in the old pending slots
          const oldIndex = localSlots.findIndex((s) => s === slot);
          if (oldIndex !== -1 && pendingIndices.has(oldIndex)) {
            const pendingData = pendingSlots[day].get(oldIndex);
            if (pendingData) {
              newPendingSlots.set(newIndex, pendingData);
            }
          }
        }
      });

      // Replace the pending slots for this day
      pendingSlots[day].clear();
      newPendingSlots.forEach((data, index) => {
        pendingSlots[day].set(index, data);
      });
    });

    // Clean up deleted slot IDs only if there are no pending operations
    if (pendingUpdateOperations.size === 0) {
      locallyDeletedSlotIds.clear();
    }
  },
  { immediate: true },
);

let debounceTimeout: ReturnType<typeof setTimeout>;

function debounce(fn: () => void, delay: number) {
  clearTimeout(debounceTimeout);

  debounceTimeout = setTimeout(fn, delay);
}

function initializeSlotsFromData(availability: AvailabilitySlot[]) {
  const availabilityByWeekDay: SlotsByDay = {
    ...initializeEmptySlots(),
  };

  availability.forEach((slot) => availabilityByWeekDay[slot.weekday].push(slot));

  return availabilityByWeekDay;
}

function initializeEmptySlots(): { [value in WeekDay]: any[] } {
  return {
    [WeekDay.SUNDAY]: [],
    [WeekDay.MONDAY]: [],
    [WeekDay.TUESDAY]: [],
    [WeekDay.WEDNESDAY]: [],
    [WeekDay.THURSDAY]: [],
    [WeekDay.FRIDAY]: [],
    [WeekDay.SATURDAY]: [],
  };
}

function getDefaultSlot(day: WeekDay): NewAvailabilitySlotDto {
  return {
    propertyAvailabilityId: props.investorAvailabilityId,
    weekday: day,
    startTime: '09:00',
    endTime: '17:00',
  };
}

function getEmptySlot(day: WeekDay): NewAvailabilitySlotDto {
  return {
    propertyAvailabilityId: props.investorAvailabilityId,
    weekday: day,
    startTime: '00:00',
    endTime: '00:00',
  };
}

function hasSlotsForDay(day: WeekDay): boolean {
  return toggledDays[day].length > 0;
}

function hasPendingSlotsForDay(day: WeekDay): boolean {
  return pendingSlots[day].size > 0;
}

function isPendingSlot(day: WeekDay, index: number): boolean {
  return pendingSlots[day].has(index);
}

function isPendingSlotWithoutId(day: WeekDay, index: number): boolean {
  // Only disable slots that are pending AND don't have an ID
  return isPendingSlot(day, index) && !toggledDays[day][index]?.id;
}

function markSlotAsPending(day: WeekDay, index: number, startTime: string, endTime: string): void {
  pendingSlots[day].set(index, { startTime, endTime });
}

function onSlotAdded(newSlot: NewAvailabilitySlotDto, savedSlot: AvailabilitySlot) {
  const day = newSlot.weekday;

  // Find the matching local slot by content (since it won't have an ID yet)
  const matchingSlotIndex = toggledDays[day].findIndex(
    (slot) =>
      !slot.id &&
      slot.weekday === newSlot.weekday &&
      slot.startTime === newSlot.startTime &&
      slot.endTime === newSlot.endTime,
  );

  if (matchingSlotIndex !== -1) {
    // Replace the local slot with the server slot
    toggledDays[day][matchingSlotIndex] = savedSlot;

    // Clear the pending state for this slot
    pendingSlots[day].delete(matchingSlotIndex);
  }
}

function onSlotUpdated(updatedSlot: AvailabilitySlot) {
  const day = updatedSlot.weekday;

  // Clear the pending update operation
  pendingUpdateOperations.delete(updatedSlot.id);

  // Find the slot by ID
  let index = toggledDays[day].findIndex((slot) => slot.id === updatedSlot.id);

  if (index !== -1) {
    // Get the current UI values
    const currentStartTime = toggledDays[day][index].startTime;
    const currentEndTime = toggledDays[day][index].endTime;

    // Clear the pending state
    pendingSlots[day].delete(index);

    // Update the slot with server data BUT KEEP the current time values
    // This ensures the UI never flickers back to old values
    toggledDays[day][index] = {
      ...updatedSlot,
      startTime: currentStartTime,
      endTime: currentEndTime,
    };
  }
  // If not found by ID (new slot), try to find a pending slot
  else {
    const pendingIndices = Array.from(pendingSlots[day].keys());
    if (pendingIndices.length > 0) {
      const pendingIndex = pendingIndices[0];

      // For new slots, we can use the server values
      pendingSlots[day].delete(pendingIndex);
      toggledDays[day][pendingIndex] = updatedSlot;
    }
  }

  // Clean up deleted slot IDs if there are no more pending operations
  cleanupDeletedSlotIdsIfSafe();
}

function cleanupDeletedSlotIdsIfSafe() {
  // Only clean up deleted slot IDs if there are no pending update operations
  // This prevents deleted slots from reappearing during concurrent operations
  if (pendingUpdateOperations.size === 0) {
    // It's safe to clean up - no pending operations that could trigger the watch function
    locallyDeletedSlotIds.clear();
  }
}

function onSlotsDeleted(deletedSlotIds: string[]) {
  // Don't clean up immediately - wait for any pending operations to complete
  // The cleanup will happen in the watch function when the last pending operation completes
}

defineExpose({
  onSlotAdded,
  onSlotUpdated,
  onSlotsDeleted,
});

function toggleDay(day: WeekDay, value: boolean) {
  if (value && !hasSlotsForDay(day)) {
    const slot = getDefaultSlot(day);
    toggledDays[day].push(slot);
    markSlotAsPending(day, 0, slot.startTime, slot.endTime);
    emit('slot-added', slot);
  } else if (!value) {
    const idsToDelete = toggledDays[day].map((slot) => slot.id).filter((id): id is string => !!id);

    // Track all deleted slot IDs
    idsToDelete.forEach((id) => locallyDeletedSlotIds.add(id));

    toggledDays[day] = [];
    emit('slot-deleted', idsToDelete);
  }
}

function addTimeSlot(day: WeekDay) {
  // Create a new slot
  const slot = hasSlotsForDay(day) ? getEmptySlot(day) : getDefaultSlot(day);
  const newIndex = toggledDays[day].length;

  // Add the slot to the day
  toggledDays[day].push(slot);

  // Mark as pending and validate
  markSlotAsPending(day, newIndex, slot.startTime, slot.endTime);
  validateTimeOverlap(day);

  // Emit the event
  emit('slot-added', slot);
}

function removeTimeSlot(day: WeekDay, index: number) {
  // Get the slot ID before removing it
  const slotId = toggledDays[day][index]?.id;

  if (slotId) {
    locallyDeletedSlotIds.add(slotId);
    emit('slot-deleted', [slotId]);
  }

  toggledDays[day].splice(index, 1);
  updatePendingIndicesAfterRemoval(day, index);

  // If no slots left for the day, toggle it off
  if (!hasSlotsForDay(day)) {
    toggleDay(day, false);
  }

  // Validate remaining slots
  validateTimeOverlap(day);
}

function updatePendingIndicesAfterRemoval(day: WeekDay, removedIndex: number) {
  // Get all pending indices and their data
  const pendingData = new Map(pendingSlots[day]);

  // Clear all pending slots for this day
  pendingSlots[day].clear();

  // Rebuild the pending slots map with adjusted indices
  pendingData.forEach((data, i) => {
    if (i > removedIndex) {
      // Shift indices down for slots after the removed one
      pendingSlots[day].set(i - 1, data);
    } else if (i < removedIndex) {
      // Keep the same index for slots before the removed one
      pendingSlots[day].set(i, data);
    }
    // Skip the removed index
  });
}

const isOverlapping = (
  a: Pick<AvailabilitySlot, 'startTime' | 'endTime'>,
  b: Pick<AvailabilitySlot, 'startTime' | 'endTime'>,
) => {
  let aStart = timeToMinutes(a.startTime);
  let aEnd = timeToMinutes(a.endTime);
  let bStart = timeToMinutes(b.startTime);
  let bEnd = timeToMinutes(b.endTime);

  if (aStart === 0 && aEnd === 0 && bStart === 0 && bEnd === 0) {
    return false;
  }

  if ((aStart === 0 && aEnd === 0) || (bStart === 0 && bEnd === 0)) {
    return false;
  }

  if (aEnd === 0) aEnd = 24 * 60;
  if (bEnd === 0) bEnd = 24 * 60;

  if (aStart < bEnd && bStart < aEnd) {
    return true;
  }

  if (bEnd < bStart && (aStart < bEnd || bStart < aEnd)) {
    return true;
  }

  return aEnd < aStart && (bStart < aEnd || aStart < bEnd);
};

const isValidTime = (start: string, end: string) => {
  const startTime = timeToMinutes(start);
  const endTime = timeToMinutes(end);

  if (startTime >= 12 * 60 && endTime < 12 * 60) {
    return true;
  }

  if (endTime === 0) return true;

  return startTime < endTime;
};

const timeToMinutes = (time: string) => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

const handleValueChange = (day: WeekDay, slotIndex: number) => {
  // Get the slot
  const slot = toggledDays[day][slotIndex];

  // Don't proceed if the slot doesn't have an ID
  if (!slot.id) return;

  // Don't proceed if the slot is pending and doesn't have an ID
  if (isPendingSlotWithoutId(day, slotIndex)) return;

  // Validate time overlap
  validateTimeOverlap(day);

  // Don't proceed if there are errors
  if (slotErrors[day][slotIndex]) return;

  // Capture the current values at the time of the change
  // This is important to ensure we're using the values at the time the user made the change
  const currentValues = {
    startTime: slot.startTime,
    endTime: slot.endTime,
  };

  // Mark as pending with the current values and emit update with debounce
  debounce(() => {
    // Store the values in the pending map
    markSlotAsPending(day, slotIndex, currentValues.startTime, currentValues.endTime);

    // Track this update operation
    if (slot.id) {
      pendingUpdateOperations.add(slot.id);
    }

    // Emit the update with the captured values
    emit('slot-updated', {
      ...slot,
      startTime: currentValues.startTime,
      endTime: currentValues.endTime,
    });
  }, 500);
};

const validateTimeOverlap = async (day: WeekDay): Promise<void> => {
  slotErrors[day] = [];

  for (let i = 0; i < toggledDays[day].length; i++) {
    const currentSlot = toggledDays[day][i];

    if (!isValidTime(currentSlot.startTime, currentSlot.endTime)) {
      slotErrors[day][i] = 'Start time must be before end time.';
      continue;
    }

    for (let j = i + 1; j < toggledDays[day].length; j++) {
      if (isOverlapping(currentSlot, toggledDays[day][j])) {
        slotErrors[day][i] = 'Time cannot overlap';
        slotErrors[day][j] = 'Time cannot overlap';
      }
    }
  }
};
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.availability {
  display: flex;
  flex-direction: column;
  gap: var(--t-spacing-sm);
}

.container {
  padding-block: var(--t-spacing-sm);
}

.time-slot {
  display: flex;
  flex-direction: column;
  margin-top: 0.75rem;
}

.controls {
  display: flex;
  align-items: center;
  gap: var(--t-spacing-sm);
}

.error-text {
  color: var(--t-color-red-400);
  margin-top: var(--t-spacing-sm);
  font-size: 0.9rem;
}

.time-input {
  padding: var(--t-spacing-sm) var(--t-spacing-md);
  border: 0.063rem solid var(--t-color-neutral-400);
  border-radius: var(--t-border-radius-sm);

  @include mobile {
    padding: var(--t-spacing-sm);
  }

  &::-webkit-datetime-edit-ampm-field,
  &::-webkit-calendar-picker-indicator {
    padding: 0;
  }

  &:disabled {
    cursor: not-allowed;
  }
}
</style>
