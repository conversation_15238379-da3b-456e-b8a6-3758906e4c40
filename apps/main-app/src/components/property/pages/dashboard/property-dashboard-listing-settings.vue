<template>
  <Card size="medium" id="pd-listing-settings">
    <View gap="md" direction="column">
      <!-- Screening sensitivity info -->
      <View justify="space-between" align="center" class="setting-row screening-sensitivity-row">
        <View gap="sm" align="center" class="screening-sensitivity-content">
          <Text variant="h5">Screening sensitivity</Text>
          <Badge :label="screeningSensitivity" icon="leads" variant="grey" />
        </View>
        <Button variant="ghost" size="medium" @click="editScreeningSensitivity" class="edit-button">
          <span>Edit</span>
        </Button>
      </View>

      <!-- Tour options -->
      <View justify="space-between" align="center" class="setting-row tour-options-row">
        <View gap="sm" align="center" class="tour-options-content">
          <Text variant="h5">Tour options</Text>
          <View gap="sm" align="center" class="tour-options-badges">
            <Badge v-if="allowsInPersonTours" icon="user-01" label="In-person" variant="grey" />
            <Badge v-if="allowsVirtualTours" icon="video-recorder" label="Virtual" variant="grey" />
            <Badge v-if="allowsSelfGuidedTours" icon="key" label="Self-guided" variant="grey" />
            <Text v-if="!allowsInPersonTours && !allowsVirtualTours && !allowsSelfGuidedTours" variant="label-medium">
              Not set
            </Text>
          </View>
        </View>
        <Button variant="ghost" size="medium" @click="editTourOptions" class="edit-button">
          <span>Edit</span>
        </Button>
      </View>

      <!-- Listed on platforms -->
      <View justify="space-between" align="center" class="setting-row">
        <View gap="sm" align="center">
          <Text variant="h5">Listed on</Text>
          <View gap="sm" align="center" class="platforms-container">
            <View v-if="platforms.length === 0" gap="sm" align="center">
              <Text variant="label-medium">-</Text>
            </View>
            <View v-else gap="xs" align="center">
              <div v-for="platform in platforms" :key="platform" class="platform-logo">
                <img v-if="platform === 'zillow'" :src="zillowLogo" alt="Zillow" class="logo-image" />
              </div>
            </View>
          </View>
        </View>
        <Button
          v-if="canEditSyndicationPlatforms"
          variant="ghost"
          size="medium"
          @click="editSyndicationPlatforms"
          class="edit-button"
        >
          <span>Edit</span>
        </Button>
      </View>

      <!-- Action buttons -->
      <View gap="sm" direction="row" class="actions">
        <Button
          v-if="isVisibleStartListingButton"
          variant="primary"
          size="medium"
          @click="startListing"
          class="action-button"
        >
          <Icon name="start" />
          <span>{{ startListingLabel }}</span>
        </Button>

        <Button
          v-else-if="isVisibleStopListingButton"
          variant="secondary"
          size="medium"
          @click="pauseListing"
          class="action-button"
        >
          <Icon name="pause-circle" />
          <span>Pause listing</span>
        </Button>

        <Button
          v-if="isVisibleMarkAsRentedOutButton"
          variant="primary"
          appearance="success"
          size="medium"
          @click="markAsRentedOut"
          class="action-button rented-button"
        >
          <Icon name="check-circle" />
          <span>Mark as rented out</span>
        </Button>
      </View>
    </View>

    <!-- Modals -->
    <PropertyMarkAsRentedOutModal ref="markAsRentedOutModalRef" />
    <PropertyPauseListingModal ref="pauseModalRef" />
    <PropertyListModal ref="resumeModalRef" />
    <PropertyScreeningSensitivityModal ref="screeningSensitivityModalRef" />
    <PropertyTourOptionsModal ref="tourOptionsModalRef" />
    <PropertySyndicationPlatformsModal ref="syndicationPlatformsModalRef" />
  </Card>
</template>

<script setup lang="ts">
import zillowLogo from 'icons/zillow-logo.png';
import { computed, ref } from 'vue';

import { Badge, Button, Card, Icon, Text, View } from '@tallo/design-system';
import { usePropertyStore, usePropertySyndicationPlatformsQuery } from '@tallo/investor/property';
import { PropertyStatus } from '@tallo/property';

import PropertyListModal from '../../components/property-list-modal.vue';
import PropertyMarkAsRentedOutModal from '../../components/property-mark-as-rented-out-modal.vue';
import PropertyPauseListingModal from '../../components/property-pause-listing-modal.vue';
import PropertyScreeningSensitivityModal from '../../components/property-screening-sensitivity-modal.vue';
import PropertySyndicationPlatformsModal from '../../components/property-syndication-platforms-modal.vue';
import PropertyTourOptionsModal from '../../components/property-tour-options-modal.vue';

const propertyStore = usePropertyStore();
const { platforms } = usePropertySyndicationPlatformsQuery();
const markAsRentedOutModalRef = ref<InstanceType<typeof PropertyMarkAsRentedOutModal>>();
const pauseModalRef = ref<InstanceType<typeof PropertyPauseListingModal>>();
const resumeModalRef = ref<InstanceType<typeof PropertyListModal>>();
const screeningSensitivityModalRef = ref<InstanceType<typeof PropertyScreeningSensitivityModal>>();
const tourOptionsModalRef = ref<InstanceType<typeof PropertyTourOptionsModal>>();
const syndicationPlatformsModalRef = ref<InstanceType<typeof PropertySyndicationPlatformsModal>>();

const propertyStatus = computed(() => propertyStore.property?.status!);
const screeningSensitivity = computed(
  () => propertyStore.property?.renterRequirements?.screeningSensitivity || 'Not set',
);
const allowsInPersonTours = computed(() => propertyStore.property?.allowsInPersonTours || false);
const allowsVirtualTours = computed(() => propertyStore.property?.allowsVirtualTours || false);
const allowsSelfGuidedTours = computed(() => propertyStore.property?.allowsSelfGuidedTours || false);
const startListingLabel = computed(() =>
  propertyStatus.value === PropertyStatus.READY_FOR_LISTING ? 'List property' : 'Resume listing',
);

const isVisibleStartListingButton = computed(() => {
  return [
    PropertyStatus.READY_FOR_LISTING,
    PropertyStatus.UNLISTED,
    PropertyStatus.RENTED_OUT,
    PropertyStatus.DRAFT,
  ].includes(propertyStatus.value!);
});

const isVisibleStopListingButton = computed(() => {
  return [PropertyStatus.LISTING_IN_PROGRESS, PropertyStatus.LISTED].includes(propertyStatus.value!);
});

const isVisibleMarkAsRentedOutButton = computed(() => {
  return [PropertyStatus.LISTING_IN_PROGRESS, PropertyStatus.LISTED, PropertyStatus.UNLISTED].includes(
    propertyStatus.value!,
  );
});

const canEditSyndicationPlatforms = computed(() => {
  return propertyStatus.value === PropertyStatus.LISTED;
});

function startListing() {
  resumeModalRef.value?.modalRef?.show();
}

function pauseListing() {
  pauseModalRef.value?.modalRef?.show();
}

function markAsRentedOut() {
  markAsRentedOutModalRef.value?.modalRef?.show();
}

function editScreeningSensitivity() {
  screeningSensitivityModalRef.value?.modalRef?.show();
}

function editTourOptions() {
  tourOptionsModalRef.value?.modalRef?.show();
}

function editSyndicationPlatforms() {
  syndicationPlatformsModalRef.value?.modalRef?.show();
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.actions {
  @include screen-max-xs {
    flex-direction: column;
  }
}

.action-button {
  flex-grow: 1;
}

.platforms-container {
  min-height: 1.5rem;
}

.platform-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--t-border-radius-xs);
  background-color: var(--t-color-neutral-100);
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: var(--t-border-radius-xs);
}

.setting-row {
  @media (min-width: 1024px) {
    transition: background-color 0.2s ease;
    border-radius: var(--t-border-radius-sm);
    padding: var(--t-spacing-xs);
    margin: calc(-1 * var(--t-spacing-xs));

    &:hover {
      background-color: var(--t-color-neutral-50);
    }
  }
}

.edit-button {
  @media (min-width: 1024px) {
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
  }
}

@media (min-width: 1024px) {
  .setting-row:hover .edit-button {
    opacity: 1;
    pointer-events: auto;
  }
}

@media (max-width: 1023px) {
  .edit-button {
    opacity: 1;
    pointer-events: auto;
  }
}

.screening-sensitivity-content {
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start !important;
  }
}

.tour-options-content {
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start !important;
  }
}

.tour-options-badges {
  flex-wrap: wrap;
  @media (max-width: 768px) {
    flex-wrap: wrap;
    align-items: flex-start !important;
  }
}
</style>
