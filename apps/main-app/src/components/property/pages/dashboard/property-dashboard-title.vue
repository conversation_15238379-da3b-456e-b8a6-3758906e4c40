<template>
  <div>
    <View wrap="wrap" justify="space-between" direction="row" :grow="1" gap="lg">
      <View gap="sm" class="title" wrap="wrap-reverse" align="center">
        <Text variant="h1">{{ propertyStore.property?.displayName }}</Text>
        <View gap="sm" align="center">
          <PropertyStatusLabel :status="propertyStatus" />
          <View gap="sm" v-if="propertyStatus === PropertyStatus.LISTED && listedAt">
            <Text variant="body-medium" color="secondary">∙</Text>
            <Text variant="body-medium" color="secondary">{{ listedAt }}</Text>
          </View>
        </View>
      </View>

      <View gap="sm" wrap="wrap">
        <Button class="add-new-lead-button-desktop" variant="outline" @click="addNewLead">
          <Icon name="user-plus-01" />
          <span>Add new lead</span>
        </Button>

        <Button class="add-new-lead-button-mobile" size="medium" variant="outline" @click="addNewLead">
          <Icon name="user-plus-01" />
          <span>Add new lead</span>
        </Button>
      </View>
    </View>

    <AddNewLeadModal :propertyId="propertyId" ref="addNewLeadModalModalRef" />
  </div>
</template>

<script setup lang="ts">
import { useTimeAgo } from '@vueuse/core';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Button, Icon, Text, View } from '@tallo/design-system';
import { PropertyStatusLabel, usePropertyStore } from '@tallo/investor/property';
import { PropertyStatus } from '@tallo/property';

import AddNewLeadModal from './add-new-lead-modal.vue';

const route = useRoute();
const propertyStore = usePropertyStore();
const propertyId = ref(route.params.propertyId as string);
const addNewLeadModalModalRef = ref<InstanceType<typeof AddNewLeadModal>>();
const propertyStatus = computed(() => propertyStore.property?.status!);
const listedAt = computed(() =>
  propertyStore.property?.lastListedAt ? useTimeAgo(propertyStore.property?.lastListedAt) : null,
);

function addNewLead() {
  addNewLeadModalModalRef.value?.modalRef?.show();
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.add-new-lead-button-desktop {
  @include mobile {
    display: none;
  }
}

.add-new-lead-button-mobile {
  @include tablet-start {
    display: none;
  }
}
</style>
