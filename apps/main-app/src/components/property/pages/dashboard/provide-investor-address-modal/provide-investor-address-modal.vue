<template>
  <Modal ref="modalRef" width="40rem" @after-close="clearForm">
    <template #body>
      <FormGroup>
        <View gap="md" direction="column" align="center">
          <Text variant="h2">Address Required for Applications</Text>
          <Text variant="body-large" color="secondary" align="center">
            TransUnion requires your address to process rental applications. This is a one-time setup needed before
            sending your first application.
          </Text>
        </View>
        <FormGroup>
          <FormField label="Address" :error="form.fieldErrors('location')">
            <AutocompleteAddress v-model="form.fields.location" />
          </FormField>

          <Map :coordinates="coordinates" />

          <Hint gap="md">
            <template #icon>
              <IconTile icon="info-circle" size="small" color="blue" />
            </template>

            <View direction="column" gap="sm">
              <Text variant="h5">Important information</Text>
              <Text variant="body-medium">
                You can use either your business or home address. This information is only shared with TransUnion for
                verification purposes and will not be visible to renters or displayed publicly.
              </Text>
              <div>
                <Link
                  class="learn-more-link"
                  color="accent"
                  target="_blank"
                  to="https://files.tallo.ai/public/trans-union-terms-and-conditions.pdf"
                >
                  View TransUnion terms and conditions
                </Link>
              </div>
            </View>
          </Hint>
        </FormGroup>
      </FormGroup>
    </template>

    <template #footer>
      <View gap="md" :grow="1" class="actions">
        <Button variant="ghost" @click="close">Cancel</Button>
        <Button
          variant="primary"
          :disabled="!form.fields.location?.state"
          :loading="isSavingAddress"
          @click="saveAddress"
        >
          Save Address & Continue
        </Button>
      </View>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { helpers, required } from '@vuelidate/validators';
import { computed, ref, watch } from 'vue';

import {
  Button,
  FormField,
  FormGroup,
  Hint,
  IconTile,
  Link,
  Modal,
  Text,
  View,
  useAlertsStore,
  useForm,
} from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { AutocompleteAddress, addressService } from '@tallo/investor/property';
import { AddressDto, Map } from '@tallo/property';

const alertsStore = useAlertsStore();
const investorStore = useInvestorStore();
const modalRef = ref<InstanceType<typeof Modal>>();
const isSavingAddress = ref(false);

const emit = defineEmits(['addressSaved']);

const form = useForm<{
  location: AddressDto | null;
}>(
  {
    location: null,
  },
  {
    location: { required: helpers.withMessage('Address is required', required) },
  },
);

const coordinates = computed(() => {
  const loc = form.fields.location;
  if (loc?.latitude && loc?.longitude) {
    return {
      latitude: loc.latitude,
      longitude: loc.longitude,
    };
  }
  return undefined;
});

function saveAddress() {
  isSavingAddress.value = true;
  investorStore
    .updateAddress({
      address: form.fields.location!.address.split(',')[0],
      city: form.fields.location!.city,
      state: form.fields.location!.state,
      zip: form.fields.location!.zip,
      latitude: form.fields.location!.latitude,
      longitude: form.fields.location!.longitude,
    })
    .then(() => {
      alertsStore.success('Address saved successfully');
      close();
      emit('addressSaved');
    })
    .catch((err) => alertsStore.catchError(err))
    .finally(() => {
      isSavingAddress.value = false;
    });
}

watch(
  () => form.fields.location,
  async (newValue, oldValue) => {
    if (oldValue && newValue?.zip === oldValue.zip) {
      return;
    }

    if (newValue && form.fields.location !== investorStore.address) {
      const address = encodeURI(`${newValue.address}, ${newValue.city}`);
      const details = await addressService.getAddressDetails(address);

      if (details) {
        form.fields.location = {
          address: details.address,
          city: details.city,
          zip: details.zip,
          state: details.state,
          latitude: details.latitude,
          longitude: details.longitude,
        };
      }
    }
  },
);

function close() {
  modalRef.value?.close();
}

function clearForm() {
  form.fields = {
    location: null,
  };
}

defineExpose({ modalRef });
</script>

<style scoped lang="scss">
.actions > * {
  width: 100%;
}
</style>
