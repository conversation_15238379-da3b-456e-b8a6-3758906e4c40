<template>
  <View gap="lg" direction="column" id="pd-showing-requests">
    <View justify="space-between">
      <Text variant="h2" ref="showingRequestsSection">
        Showing requests
        <span v-if="hasShowingRequests">({{ showingRequestsAmount }})</span>
      </Text>
      <ScrollableHorizontallyControls :scrollable-container="showingRequestsScrollableContainerRef" />
    </View>

    <div>
      <ScrollableHorizontallyContainer ref="showingRequestsScrollableContainerRef">
        <View class="card-items" gap="sm" padding-bottom="md" v-if="hasShowingRequests">
          <ShowingCard
            v-for="showing in pendingShowings"
            :key="showing.id"
            :showing="showing"
            :show-status="false"
            class="card-item"
          />
        </View>
      </ScrollableHorizontallyContainer>

      <Card variant="outline" size="medium" class="hint" v-if="!hasShowingRequests">
        <template #header>
          <Text variant="h4">What are Showing requests?</Text>
        </template>
        <Text variant="body-medium" color="secondary">
          <PERSON><PERSON> will handle the back and forth with <PERSON><PERSON>, and will find the best time that works within your
          availability. All requests need to be approved and we will display them here.
        </Text>
      </Card>
    </div>
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  Card,
  ScrollableHorizontallyContainer,
  ScrollableHorizontallyControls,
  Text,
  View,
} from '@tallo/design-system';
import { ShowingRequestStatus, ShowingCard, useShowings } from '@tallo/investor/showing';

const route = useRoute();
const propertyId = computed(() => route.params.propertyId as string);

const showingRequestsScrollableContainerRef = ref();

const { pendingShowings } = useShowings(propertyId);
const showingRequestsAmount = computed(() => {
  const pendingShowingRequests = pendingShowings.value.flatMap((showing) => {
    return showing.showingRequests.filter(({ status }) => status === ShowingRequestStatus.PENDING);
  });

  return pendingShowingRequests.length;
});
const hasShowingRequests = computed(() => showingRequestsAmount.value > 0);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.card-items {
  .card-item {
    width: 28.3333rem;
    flex-shrink: 0;
  }

  @include mobile {
    flex-direction: column;

    .card-item {
      width: 100%;
    }
  }
}

.hint {
  max-width: 28.3333rem;
}
</style>
