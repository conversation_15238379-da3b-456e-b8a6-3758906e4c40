<template>
  <Card size="medium" id="pd-auto-update">
    <View gap="md" align="start">
      <IconTile icon="refresh" size="medium" color="neutral" />

      <View direction="column" gap="sm" class="content">
        <View direction="column">
          <View gap="xs" align="start">
            <Text variant="label-medium">Listing optimization</Text>
            <div class="tooltip-container">
              <Tooltip
                icon-size="extra-small"
                content="Tall<PERSON> will automatically make slight changes to your listing to refresh it and maintain top position in search results. This feature only works for properties syndicated through Tallo"
              />
            </div>
          </View>
          <Text variant="body-medium" color="tertiary">Automatically refreshes to keep listing top-ranked</Text>
        </View>
        <Text variant="label-medium" color="secondary">
          {{ lastUpdatedText }}
        </Text>
      </View>

      <Switch v-model="isAutoUpdateEnabled" />
    </View>
  </Card>
</template>

<script setup lang="ts">
import { useTimeAgo } from '@vueuse/core';
import { ref, computed } from 'vue';

import { Card, IconTile, Switch, Text, Tooltip, View } from '@tallo/design-system';
import { usePropertyStore } from '@tallo/investor/property';
import { useTimeAgoShortMessages } from '@tallo/utility';

const propertyStore = usePropertyStore();

const isAutoUpdateEnabled = computed({
  get: () => propertyStore.property?.autoRefresh ?? false,
  set: (value: boolean) => {
    propertyStore.toggleAutoRefresh(value);
  },
});

const lastUpdatedText = computed(() => {
  const lastListedAt = propertyStore.property?.lastListedAt;

  if (!lastListedAt) {
    return 'Never refreshed';
  }

  const timeAgo = useTimeAgo(lastListedAt, { messages: useTimeAgoShortMessages });
  return `Refreshed ${timeAgo.value}`;
});
</script>

<style scoped lang="scss">
.content {
  flex: 1;
}

.tooltip-container {
  width: 1rem;
  height: 1.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
</style>
