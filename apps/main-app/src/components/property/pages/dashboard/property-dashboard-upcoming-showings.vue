<template>
  <View gap="lg" direction="column" id="pd-upcoming-showings">
    <View justify="space-between">
      <Text variant="h2">Upcoming Showings</Text>
      <ScrollableHorizontallyControls :scrollable-container="scrollableContainerRef" />
    </View>

    <Text variant="body-large" color="tertiary" class="hint" v-if="!hasUpcomingShowings">
      <div>Here you'll be able to see the upcoming showings.</div>
      <div>We'll also notify you with them.</div>
    </Text>

    <ScrollableHorizontallyContainer ref="scrollableContainerRef">
      <View class="card-items upcoming-showings-block" gap="sm" padding-bottom="md">
        <View
          gap="sm"
          direction="column"
          v-for="upcomingShowing in groupedByDateUpcomingShowings"
          :key="upcomingShowing.date.toString()"
        >
          <View gap="sm">
            <Text variant="h4">{{ useDateFormat(upcomingShowing.date, 'MMM D') }}</Text>
            <Text variant="body-large" color="tertiary">
              {{ useDateFormat(upcomingShowing.date, 'dddd') }}
            </Text>
          </View>

          <View gap="sm" direction="column">
            <ShowingCard
              class="showing"
              v-for="showing in upcomingShowing.showings"
              :key="showing.id"
              :showing="showing"
              :show-status="false"
              :show-showing-requests="false"
            />
          </View>
        </View>
      </View>
    </ScrollableHorizontallyContainer>
  </View>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

import { ScrollableHorizontallyContainer, ScrollableHorizontallyControls, Text, View } from '@tallo/design-system';
import { ShowingCard, useShowings } from '@tallo/investor/showing';

const route = useRoute();
const propertyId = computed(() => route.params.propertyId as string);
const { groupedByDateUpcomingShowings } = useShowings(propertyId);

const scrollableContainerRef = ref();
const hasUpcomingShowings = computed(() => groupedByDateUpcomingShowings.value.length);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.card-items {
  .showing {
    width: 28.3333rem;
    flex-shrink: 0;
  }

  @include mobile {
    flex-direction: column;

    .showing {
      width: 100%;
    }
  }
}

@include mobile {
  .upcoming-showings-block {
    gap: var(--t-spacing-lg);
  }
}

.hint {
  max-width: 28.3333rem;
}
</style>
