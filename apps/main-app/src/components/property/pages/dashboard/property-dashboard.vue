<template>
  <PageContainer v-if="isPropertyLoaded">
    <View gap="xl" direction="column">
      <PropertyDashboardTitle />
      <View direction="column" gap="sm" class="top-panel">
        <PropertyDashboardStats :property-id="propertyId" class="stats" />
        <View gap="sm" direction="row" class="widgets">
          <PropertyDashboardAutoUpdate class="widget-item" />
          <PropertyDashboardListingSettings class="widget-item" />
        </View>
      </View>

      <div ref="showingRequestsSection">
        <PropertyDashboardShowingRequests />
      </div>

      <div ref="escalationsSection">
        <PropertyDashboardEscalations />
      </div>

      <PropertyDashboardUpcomingShowings />
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { until } from '@vueuse/core';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { PageContainer, View } from '@tallo/design-system';
import { usePropertyStore } from '@tallo/investor/property';

import productTourService from '../../../product-tour/product-tour.service';
import PropertyDashboardAutoUpdate from './property-dashboard-auto-update.vue';
import PropertyDashboardEscalations from './property-dashboard-escalations.vue';
import PropertyDashboardListingSettings from './property-dashboard-listing-settings.vue';
import PropertyDashboardShowingRequests from './property-dashboard-showing-requests.vue';
import PropertyDashboardStats from './property-dashboard-stats.vue';
import PropertyDashboardTitle from './property-dashboard-title.vue';
import PropertyDashboardUpcomingShowings from './property-dashboard-upcoming-showings.vue';

const route = useRoute();
const propertyId = computed(() => route.params.propertyId as string);
const propertyStore = usePropertyStore();

const isPropertyLoaded = computed(() => propertyStore.isLoaded);
const property = computed(() => propertyStore.property);
const escalationsSection = ref<HTMLElement>();
const showingRequestsSection = ref<HTMLElement>();

onMounted(() => {
  scrollToSection();
});

watch(
  property,
  (newProperty) => {
    if (newProperty) {
      productTourService.tryStartDashboardTour(propertyId.value);
    }
  },
  { immediate: true },
);

async function scrollToSection() {
  await until(isPropertyLoaded).toMatch((isLoaded) => !!isLoaded);

  const section = getSectionToScroll();

  if (section) {
    setTimeout(() => section.scrollIntoView({ behavior: 'smooth' }), 100);
  }
}

function getSectionToScroll(): HTMLElement | undefined {
  switch (route.hash) {
    case '#escalations-section':
      return escalationsSection.value;

    case '#showing-requests-section':
      return showingRequestsSection.value;
  }
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.top-panel {
  @media screen and (min-width: 75rem) {
    flex-direction: row !important;
  }
}

.widgets {
  @media screen and (min-width: 75rem) {
    width: 45%;
    flex-direction: column !important;
  }

  @media screen and (max-width: 57.5rem) {
    flex-direction: column !important;
  }
}

.widget-item {
  width: 50%;

  @media screen and (min-width: 75rem) {
    width: 100%;
  }

  @media screen and (max-width: 57.5rem) {
    width: 100%;
  }
}

.stats {
  width: 100%;

  @media screen and (min-width: 75rem) {
    width: 65%;
  }
}
</style>
