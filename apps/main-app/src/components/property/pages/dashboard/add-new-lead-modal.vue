<template>
  <Modal position="end" ref="modalRef" @after-close="clearForm">
    <template #header>Add new lead</template>

    <template #body>
      <FormGroup>
        <View gap="sm" direction="column">
          <Text variant="h2">Add new lead’s info</Text>
          <Text variant="body-large" color="tertiary">
            If you already have someone interested in your property, add them here and <PERSON><PERSON> will handle the
            communication and scheduling
          </Text>
        </View>

        <FormField label="Full name" :error="form.fieldErrors('name')">
          <Input v-model="form.fields.name" placeholder="Type full name" />
        </FormField>

        <FormField label="Phone number" :error="form.fieldErrors('phone')">
          <InputPhone v-model="form.fields.phone" />
        </FormField>

        <FormField label="Email" :error="form.fieldErrors('email')">
          <Input v-model="form.fields.email" type="email" placeholder="Type email" />
        </FormField>

        <FormField label="Choose what you want <PERSON><PERSON> to do" :error="form.fieldErrors('action')">
          <RadioButtonGroup direction="column">
            <RadioButton v-model="form.fields.action" name="action" :value="LeadAction.SCHEDULE_SHOWING">
              <View direction="column" gap="sm">
                <Text variant="label-medium">{{ LeadAction.SCHEDULE_SHOWING }}</Text>
                <Text variant="body-medium" color="secondary">
                  Tallo will reach out the lead and schedule the showing
                  <br />
                  based on your availability. You will get a showing
                  <br />
                  request once time is found
                </Text>
              </View>
            </RadioButton>
            <RadioButton v-model="form.fields.action" name="action" :value="LeadAction.SEND_APPLICATION">
              <View direction="column" gap="sm">
                <Text variant="label-medium">{{ LeadAction.SEND_APPLICATION }}</Text>
                <Text variant="body-medium" color="secondary">
                  Tallo will send the lead a link to fill in the application.
                  <br />
                  You are gonna be able to track application progress
                  <br />
                  in the lead’s profile
                </Text>
              </View>
            </RadioButton>
          </RadioButtonGroup>
        </FormField>
      </FormGroup>
    </template>

    <template #footer>
      <Button variant="ghost" @click="modalRef?.close">Cancel</Button>
      <Button @click="save" :loading="form.submitting">Add</Button>
    </template>
  </Modal>

  <ProvideInvestorAddressModal @addressSaved="handleAddressSaved" ref="provideInvestorAddressModalRef" />

  <SendApplicationModal
    v-if="createdInquiry && createdInquiryId"
    :renter="createdInquiry.renter"
    :property-inquiry="createdInquiry"
    :inquiry-id="createdInquiryId"
    :load-bundles="false"
    :is-showing-happened="false"
    :attestations="attestations"
    ref="sendApplicationModalRef"
    @application-sent="handleApplicationSent"
  />
</template>

<script setup lang="ts">
import { useQueryCache } from '@pinia/colada';
import { email, helpers, required } from '@vuelidate/validators';
import { AxiosError } from 'axios';
import { onMounted, ref } from 'vue';

import {
  phoneNumberValidator,
  Button,
  FormField,
  FormGroup,
  Input,
  InputPhone,
  Modal,
  RadioButton,
  RadioButtonGroup,
  Text,
  View,
  useAlertsStore,
  useForm,
} from '@tallo/design-system';
import { applicationsService, PropertyAttestationsDto } from '@tallo/investor/applications';
import { useInvestorStore } from '@tallo/investor/investor';
import { getPropertyStatisticsQueryKey, PropertyInquiryDto, propertyInquiryService } from '@tallo/investor/property';
import { LeadAction, leadsService } from '@tallo/investor/renter';

import SendApplicationModal from '../../../renter-profile/components/send-application-modal/send-application-modal.vue';
import ProvideInvestorAddressModal from './provide-investor-address-modal/provide-investor-address-modal.vue';

const props = defineProps<{ propertyId: string }>();
const alertsStore = useAlertsStore();
const queryCache = useQueryCache();
const modalRef = ref<InstanceType<typeof Modal>>();
const provideInvestorAddressModalRef = ref<InstanceType<typeof ProvideInvestorAddressModal>>();
const sendApplicationModalRef = ref<InstanceType<typeof SendApplicationModal>>();
const investorStore = useInvestorStore();

// State for send application modal
const createdInquiry = ref<PropertyInquiryDto | null>(null);
const createdInquiryId = ref<string | null>(null);
const attestations = ref<PropertyAttestationsDto | null>(null);
const isFetchingAttestations = ref(false);

onMounted(async () => {
  if (!investorStore.investor) {
    await investorStore.load();
  }
});

const form = useForm<{
  name: string;
  phone: string | undefined;
  email: string;
  action: LeadAction;
}>(
  {
    name: '',
    phone: undefined,
    email: '',
    action: LeadAction.SCHEDULE_SHOWING,
  },
  {
    name: {
      required: helpers.withMessage('Full name is required', required),
    },
    phone: {
      phoneNumberValidator,
    },
    email: {
      email: helpers.withMessage('Email is not valid', email),
      required: helpers.withMessage('Email is required', required),
    },
    action: {
      required: helpers.withMessage('Please select an action', required),
    },
  },
);

function save() {
  // filter empty fields from form and then submit
  Object.keys(form.fields).forEach((key) => {
    if (!form.fields[key as keyof typeof form.fields] && key !== 'action') {
      delete form.fields[key as keyof typeof form.fields];
    }
  });

  // Ensure action is always set, defaulting to schedule_showing
  if (!form.fields.action) {
    form.fields.action = LeadAction.SCHEDULE_SHOWING;
  }

  form.submit(async () => {
    if (form.fields.action === LeadAction.SEND_APPLICATION) {
      if (!investorStore.address.address) {
        // Show address modal if address is not set up
        provideInvestorAddressModalRef.value?.modalRef?.show();
        // Return a resolved promise to prevent form submission until address is provided
        return Promise.resolve();
      }
    }

    return submitLead();
  });
}

function submitLead() {
  if (form.fields.action === LeadAction.SCHEDULE_SHOWING) {
    // For schedule showing, use the regular /leads endpoint
    return leadsService
      .add({ propertyId: props.propertyId, ...form.fields })
      .then((response) => {
        alertsStore.success(`Tallo will contact <b>${form.fields.name}</b> shortly`);
        queryCache.invalidateQueries({ key: getPropertyStatisticsQueryKey(props.propertyId) });
        modalRef.value?.close();
        return response;
      })
      .catch((err) => {
        if (err instanceof AxiosError && !err.response?.data?.validationErrors) {
          if (err.response?.data?.message?.message) {
            alertsStore.error(err.response.data.message.message);
          } else {
            alertsStore.somethingWentWrong();
          }
        }
        throw err;
      });
  } else if (form.fields.action === LeadAction.SEND_APPLICATION) {
    // For send application, use the silent endpoint and show the send application modal
    return handleSendApplicationFlow();
  }
}

async function handleSendApplicationFlow() {
  try {
    isFetchingAttestations.value = true;

    // Use the silent endpoint to create the lead and get a real inquiry ID
    const { inquiryId } = await leadsService.addSilent({
      propertyId: props.propertyId,
      ...form.fields,
    });

    // Store the inquiry ID for the modal
    createdInquiryId.value = inquiryId;

    // Fetch the created inquiry with all the proper data using the inquiryId
    const inquiries = await propertyInquiryService.getAllInquiriesByProperty(props.propertyId);
    createdInquiry.value = inquiries.find((inquiry) => inquiry.id === inquiryId) || null;

    if (!createdInquiry.value) {
      throw new Error(`Failed to find created inquiry with ID: ${inquiryId}`);
    }

    // Fetch attestations for the property
    attestations.value = await applicationsService.getPropertyAttestations(props.propertyId);

    // Show the send application modal
    sendApplicationModalRef.value?.modalRef?.show();
  } catch (error) {
    alertsStore.catchError(error);
  } finally {
    isFetchingAttestations.value = false;
  }
}

function handleAddressSaved() {
  // After address is saved, submit the lead
  // Use a small timeout to ensure the address is fully saved and the store is updated
  setTimeout(() => {
    // Make sure we're still in the form submission context
    form.submit(() => {
      return submitLead();
    });
  }, 200);
}

function handleApplicationSent() {
  // When the application is sent successfully, show success message and close the add lead modal
  alertsStore.success(`Application has been sent to <b>${form.fields.name}</b>`);

  // Clean up state
  createdInquiry.value = null;
  createdInquiryId.value = null;
  attestations.value = null;

  // Invalidate queries and close the add lead modal
  queryCache.invalidateQueries({ key: getPropertyStatisticsQueryKey(props.propertyId) });
  modalRef.value?.close();
}

function clearForm() {
  Object.assign(form.fields, {
    name: '',
    phone: '',
    email: '',
    action: LeadAction.SCHEDULE_SHOWING,
  });

  // Also clear send application modal state
  createdInquiry.value = null;
  createdInquiryId.value = null;
  attestations.value = null;
}

defineExpose({ modalRef });
</script>
