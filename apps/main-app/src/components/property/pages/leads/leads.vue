<template>
  <PageContainer>
    <View gap="lg" direction="column">
      <View justify="space-between" class="header" align="center">
        <Text variant="h1">{{ title }}</Text>

        <MenuButton :buttonLabel="selectedTimeFilter" size="medium">
          <MenuButtonItem
            v-for="(filter, index) in timeFilters"
            :key="index"
            :title="filter"
            @click="onFilterSelected(filter)"
          />
        </MenuButton>
      </View>

      <TopBarFilters :selected-filter="selectedFilter" :filters="filters" @selected="filterLeads" />

      <View v-if="state.status === 'pending'" justify="center" padding="xl">
        <LoadingIndicator />
      </View>

      <EmptyPagePlaceholder
        v-else-if="state.status === 'error'"
        title="Could not load data"
        description="Please reload the page or try again later"
      />

      <template v-else>
        <EmptyPagePlaceholder
          v-if="filteredInquiries.length === 0"
          class="empty-state"
          :title="emptyResultTitle"
          :description="emptyResultDescription"
        />

        <div v-else class="renters">
          <RenterProfileLink
            v-for="propertyInquiry in filteredInquiries"
            :decoration="false"
            :key="propertyInquiry.renter.id"
            :property-id="propertyId"
            :renter-id="propertyInquiry.renter.id"
          >
            <LeadCard :property-inquiry="propertyInquiry" />
          </RenterProfileLink>
        </div>
      </template>
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  TopBarFilters,
  EmptyPagePlaceholder,
  PageContainer,
  Card,
  Text,
  View,
  MenuButtonItem,
  MenuButton,
  LoadingIndicator,
} from '@tallo/design-system';
import { usePropertyInquiriesQuery } from '@tallo/investor/property';
import { RenterProfileLink } from '@tallo/investor/renter';
import { RentStage } from '@tallo/property';
import { TimeFilter, timeFilters } from '@tallo/utility';

import LeadCard from './lead-card.vue';

const props = defineProps<{
  title: string;
  emptyResultTitle: string;
  emptyResultDescription: string;
  filters: string[];
  filtersMap: Map<string, RentStage>;
}>();

const router = useRouter();
const route = useRoute();
const { state, inquiries, selectedTimeFilter } = usePropertyInquiriesQuery();

const propertyId = computed(() => route.params.propertyId as string);
const selectedFilter = computed(() => route.query.filter as string);
const filteredInquiries = computed(() => {
  const selectedStages = props.filtersMap.get(selectedFilter.value);

  if (!selectedStages) {
    return inquiries.value;
  }

  return inquiries.value.filter((inquiry) => selectedStages.includes(inquiry.stage));
});

function filterLeads(filter: string) {
  router.replace({ query: { ...route.query, filter } });
}

function onFilterSelected(filter: TimeFilter) {
  selectedTimeFilter.value = filter;
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.header {
  width: 100%;
}

.renters {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-sm);

  @include screen-max-sm {
    grid-template-columns: 1fr;
  }
}
</style>
