<template>
  <PageContainer v-if="property" class="property-container">
    <PropertyActions class="full-size" />
    <PropertyTitle class="full-size" />
    <PropertyImages class="full-size" />
    <PropertyLeaseConditions />
    <PropertyRenterRequirements />
    <PropertyTermsAndAvailability />
    <PropertyDescription />
    <PropertyGeneralInfo />
    <PropertyPetPolicy />
    <PropertyParking />
    <PropertyIncludedUtilities />
    <PropertyAmenities />
    <PropertyAccessibility />
    <PropertyLocation class="full-size" />
  </PageContainer>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';

import { PageContainer } from '@tallo/design-system';
import { usePropertyStore } from '@tallo/investor/property';

import productTourService from '../../../product-tour/product-tour.service';
import PropertyAccessibility from './accessibility/property-accessibility.vue';
import PropertyAmenities from './amenities/property-amenities.vue';
import PropertyDescription from './description/property-description.vue';
import PropertyGeneralInfo from './general-details/property-general-details.vue';
import PropertyIncludedUtilities from './included-utilities/property-included-utilities.vue';
import PropertyLeaseConditions from './lease-conditions/property-lease-conditions.vue';
import PropertyLocation from './location/property-location.vue';
import PropertyParking from './parking/property-parking.vue';
import PropertyPetPolicy from './pet-policy/property-pet-policy.vue';
import PropertyActions from './property-actions/property-actions.vue';
import PropertyImages from './property-images/property-images.vue';
import PropertyTitle from './property-title/property-title.vue';
import PropertyRenterRequirements from './renter-requirements/renter-requirements.vue';
import PropertyTermsAndAvailability from './terms-and-availability/property-terms-and-availability.vue';

const route = useRoute();
const propertyStore = usePropertyStore();
const propertyId = computed(() => route.params.propertyId as string);
const property = computed(() => propertyStore.property);

watch(
  property,
  (newProperty) => {
    if (newProperty) {
      productTourService.tryStartPropertyTour(propertyId.value);
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.property-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--t-spacing-sm);
}

.property-container {
  .full-size {
    grid-column: 1 / span 2;
  }

  @include mobile {
    grid-template-columns: 1fr;

    .full-size {
      grid-column: 1;
      overflow: hidden;
    }
  }
}
</style>
