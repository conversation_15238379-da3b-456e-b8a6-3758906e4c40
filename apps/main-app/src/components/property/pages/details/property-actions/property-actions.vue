<template>
  <div padding-block="md" :grow="1">
    <div class="wrapper">
      <View gap="sm" class="title">
        <Text variant="h1">Property details</Text>
      </View>

      <Button
        id="prop-list-btn"
        @click="listProperty"
        size="large"
        variant="primary"
        class="publish-button"
        :loading="updatingListingStatus"
        v-if="property!.status !== PropertyStatus.LISTED && property!.status !== PropertyStatus.LISTING_IN_PROGRESS"
      >
        List property
      </Button>
      <PropertyMenuButton class="menu-button" />
    </div>

    <PropertyListModal ref="listModalRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

import { Button, Text, View } from '@tallo/design-system';
import { usePropertyStore } from '@tallo/investor/property';
import { PropertyStatus } from '@tallo/property';

import PropertyListModal from '../../../components/property-list-modal.vue';
import PropertyMenuButton from '../../../components/property-menu-button.vue';

const propertyStore = usePropertyStore();
const property = computed(() => propertyStore.property);
const updatingListingStatus = ref<boolean>(false);

const listModalRef = ref<InstanceType<typeof PropertyListModal>>();

function listProperty() {
  listModalRef.value?.modalRef?.show();
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';
.wrapper {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--t-spacing-sm);
  align-items: center;
  width: 100%;

  &:has(> .publish-button) {
    grid-template-columns: 1fr auto auto;
  }

  @include tablet {
    grid-template-columns: 1.5fr auto;
    gap: var(--t-spacing-md);

    .publish-button {
      order: 3;
      grid-column: 1 / span 2;
      width: 100%;
    }
  }

  .publish-button,
  .menu-button {
    justify-self: end;
  }
}
</style>
