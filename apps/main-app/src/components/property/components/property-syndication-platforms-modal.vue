<template>
  <Modal position="end" ref="modalRef">
    <template #header>Syndication platforms</template>
    <template #body>
      <View direction="column" gap="lg">
        <Text variant="body-medium" color="secondary">
          Choose where your listing will be published. Your unit will be listed on the selected platforms.
        </Text>

        <FormGroup>
          <FormField label="Available platforms" direction="column">
            <View direction="column" gap="md">
              <!-- Zillow Platform -->
              <Card variant="outline" size="small" class="platform-card" :class="{ selected: form.fields.zillow }">
                <View align="center" justify="space-between">
                  <View direction="row" gap="md" align="center" class="platform-info">
                    <View class="platform-icon">
                      <img :src="zillowLogo" alt="Zillow" class="platform-logo" />
                    </View>
                    <View direction="column" gap="xs">
                      <Text variant="label-medium" color="primary" class="platform-name">Zillow</Text>
                      <Text variant="body-small" color="secondary">Also includes Hotpads and Trulia</Text>
                    </View>
                  </View>
                  <Switch v-model="form.fields.zillow" />
                </View>
              </Card>
            </View>
          </FormField>
        </FormGroup>
      </View>
    </template>
    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <Button @click="save" :loading="form.submitting || isUpdating">Save</Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import zillowLogo from 'icons/zillow-logo.png';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { Button, Card, FormField, FormGroup, Hint, Modal, Switch, Text, useForm, View } from '@tallo/design-system';
import { usePropertySyndicationPlatformsQuery, useUpdateSyndicationPlatforms } from '@tallo/investor/property';

interface SyndicationPlatformsForm {
  zillow: boolean;
}

const route = useRoute();
const propertyId = computed(() => route.params.propertyId as string);

const { platforms } = usePropertySyndicationPlatformsQuery();
const { mutateAsync: updateSyndicationPlatforms, isLoading: isUpdating } = useUpdateSyndicationPlatforms();
const modalRef = ref<InstanceType<typeof Modal>>();

const form = useForm<SyndicationPlatformsForm>({
  zillow: false,
});

// Initialize form based on current syndication platforms
watch(
  () => platforms.value,
  (currentPlatforms) => {
    form.fields.zillow = currentPlatforms.includes('zillow');
  },
  { immediate: true },
);

async function save() {
  await form.submit(async (fields) => {
    try {
      await updateSyndicationPlatforms({
        propertyId: propertyId.value,
        syndicationDto: fields,
      });

      close();
    } catch (error) {
      console.error('Error saving syndication platforms:', error);
    }
  });
}

function close() {
  modalRef.value?.close();
}

defineExpose({
  modalRef,
});
</script>

<style scoped lang="scss">
.platform-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--t-color-neutral-300);

  &:hover {
    border-color: var(--t-color-primary-400);
    background-color: var(--t-color-primary-50);
  }

  &.selected {
    border-color: var(--t-color-primary-500);
    background-color: var(--t-color-primary-100);
  }
}

.platform-info {
  flex: 1;
}

.platform-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--t-border-radius-sm);
  background-color: var(--t-color-neutral-100);
  flex-shrink: 0;
}

.platform-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: var(--t-border-radius-xs);
}

.platform-name {
  font-weight: 600;
}
</style>
