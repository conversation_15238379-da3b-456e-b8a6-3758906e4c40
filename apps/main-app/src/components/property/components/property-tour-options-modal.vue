<template>
  <Modal position="end" ref="modalRef">
    <template #header>Tour options</template>
    <template #body>
      <View direction="column" gap="lg">
        <Text variant="body-medium" color="secondary">
          Select the tour options you want to offer to potential tenants. At least one option must be selected.
        </Text>

        <FormGroup>
          <FormField label="Available tour types" direction="column">
            <View direction="column" gap="md">
              <CheckboxCard
                title="In-person tour"
                description="Showcasing your property to tenants personally or by a leasing agent"
                icon="user-01"
                v-model="form.fields.allowsInPersonTours"
              />
              <CheckboxCard
                title="Self-guided tour"
                description="Showcasing your property to tenants by providing them with instructions to access the property"
                icon="key"
                v-model="form.fields.allowsSelfGuidedTours"
              />
              <CheckboxCard
                title="Virtual tour"
                description="Showcasing your property to tenants online via apps like FaceTime or Zoom"
                icon="video-recorder"
                v-model="form.fields.allowsVirtualTours"
              />
            </View>
          </FormField>

          <Hint>At least one tour option must be selected to allow potential renters to view your property.</Hint>
        </FormGroup>
      </View>
    </template>
    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <Button @click="save" :loading="form.submitting" :disabled="!isFormValid">Save</Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { Button, CheckboxCard, FormField, FormGroup, Hint, Modal, Text, useForm, View } from '@tallo/design-system';
import { usePropertyStore } from '@tallo/investor/property';

interface TourOptionsForm {
  allowsInPersonTours: boolean;
  allowsVirtualTours: boolean;
  allowsSelfGuidedTours: boolean;
}

const propertyStore = usePropertyStore();
const modalRef = ref<InstanceType<typeof Modal>>();

const form = useForm<TourOptionsForm>({
  allowsInPersonTours: propertyStore.property?.allowsInPersonTours ?? false,
  allowsVirtualTours: propertyStore.property?.allowsVirtualTours ?? false,
  allowsSelfGuidedTours: propertyStore.property?.allowsSelfGuidedTours ?? false,
});

const isFormValid = computed(() => {
  return form.fields.allowsInPersonTours || form.fields.allowsVirtualTours || form.fields.allowsSelfGuidedTours;
});

// Reset form when property changes
watch(
  () => propertyStore.property,
  (property) => {
    if (property) {
      form.fields.allowsInPersonTours = property.allowsInPersonTours ?? false;
      form.fields.allowsVirtualTours = property.allowsVirtualTours ?? false;
      form.fields.allowsSelfGuidedTours = property.allowsSelfGuidedTours ?? false;
    }
  },
  { immediate: true },
);

async function save() {
  await form.submit(async (fields) => {
    await propertyStore.saveTourTypes(fields);
    close();
  });
}

function close() {
  modalRef.value?.close();
}

defineExpose({
  modalRef,
});
</script>

<style scoped lang="scss">
// No additional styles needed
</style>
