<template>
  <Modal position="end" ref="modalRef">
    <template #header>Screening sensitivity</template>
    <template #body>
      <View direction="column" gap="lg">
        <Text variant="body-medium" color="secondary">
          Choose how strictly you want to screen potential renters based on your requirements.
        </Text>

        <FormGroup>
          <FormField label="Renter qualification" direction="column">
            <RadioButtonGroup direction="column">
              <RadioCard
                v-for="option in screeningSensitivityOptions"
                :key="option.value"
                v-model="form.fields.screeningSensitivity"
                name="screeningSensitivity"
                :value="option.value"
              >
                <View gap="sm" direction="column">
                  <View gap="sm" direction="row" align="center">
                    <Icon :name="option.icon" size="medium" />
                    <Text variant="label-medium">{{ option.title }}</Text>
                  </View>
                  <Text variant="body-medium" color="secondary">
                    {{ option.description }}
                  </Text>
                </View>
              </RadioCard>
            </RadioButtonGroup>
          </FormField>

          <Hint>
            This information is intended to help guide <PERSON><PERSON> to foster completed conversations and won't be displayed to
            a Renter
          </Hint>
        </FormGroup>
      </View>
    </template>
    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <Button @click="save" :loading="form.submitting" :disabled="form.validation.$invalid">Save</Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import {
  Button,
  FormField,
  FormGroup,
  Hint,
  Icon,
  IconName,
  Modal,
  RadioButtonGroup,
  RadioCard,
  Text,
  useForm,
  View,
} from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { usePropertyStore } from '@tallo/investor/property';
import { ScreeningSensitivity } from '@tallo/property';

const screeningSensitivityOptions: Array<{
  value: ScreeningSensitivity;
  title: string;
  description: string;
  icon: IconName;
}> = [
  {
    value: ScreeningSensitivity.STRICT,
    title: 'Strict',
    description:
      'Only renters who meet or exceed all criteria get approved. Highest quality leads but fewer applicants overall.',
    icon: 'user-check-02',
  },
  {
    value: ScreeningSensitivity.MODERATE,
    title: 'Moderate',
    description:
      'Allows reasonable compromises on requirements while maintaining standards. Balanced quality and lead volume.',
    icon: 'users-01',
  },
  {
    value: ScreeningSensitivity.OWNER_REVIEW,
    title: 'Owner review',
    description: 'No automatic rejections. Most leads possible but requires time to manually review all applications.',
    icon: 'users-plus',
  },
];

const propertyStore = usePropertyStore();
const investorStore = useInvestorStore();
const modalRef = ref<InstanceType<typeof Modal>>();

const form = useForm<{ screeningSensitivity: ScreeningSensitivity }>({
  screeningSensitivity:
    propertyStore.property?.renterRequirements?.screeningSensitivity ||
    investorStore.investor?.preferredScreeningSensitivity ||
    ScreeningSensitivity.MODERATE,
});

async function save() {
  await form.submit(async (fields) => {
    // Use the dedicated method for updating screening sensitivity
    await propertyStore.updateScreeningSensitivity(fields.screeningSensitivity);
    close();
  });
}

function close() {
  modalRef.value?.close();
}

defineExpose({
  modalRef,
});
</script>

<style scoped lang="scss">
// No additional styles needed
</style>
