<template>
  <div class="property-navigation-container">
    <PageContainer>
      <NavPanel class="property-navigation" variant="secondary" :links="navPanelLinks" />
    </PageContainer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { PageContainer } from '@tallo/design-system';
import { AppRouteNames } from '@tallo/investor/navigation';

import { NavPanelLink } from '../root/nav-panel/nav-panel-link.interface';
import NavPanel from '../root/nav-panel/nav-panel.vue';

const route = useRoute();
const propertyId = ref(route.params.propertyId as string);

const navPanelLinks = ref<NavPanelLink[]>([
  {
    id: 'prop-nav-dashboard',
    title: 'Dashboard',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_DASHBOARD,
    params: { propertyId: propertyId.value },
  },
  {
    id: 'prop-nav-details',
    title: 'Property Details',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_DETAILS,
    params: { propertyId: propertyId.value },
  },
  {
    id: 'prop-nav-availability',
    title: 'Availability',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_AVAILABILITY,
    params: { propertyId: propertyId.value },
  },
  {
    id: 'prop-nav-showings',
    title: 'Showings',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_SHOWINGS,
    params: { propertyId: propertyId.value },
  },
  {
    id: 'prop-nav-leads',
    title: 'Leads',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_LEADS,
    params: { propertyId: propertyId.value },
  },
  {
    id: 'prop-nav-applications',
    title: 'Applications',
    appRoute: AppRouteNames.INVESTOR_PROPERTY_APPLICATIONS,
    params: { propertyId: propertyId.value },
  },
]);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.property-navigation {
  margin-inline: auto;
}
</style>
