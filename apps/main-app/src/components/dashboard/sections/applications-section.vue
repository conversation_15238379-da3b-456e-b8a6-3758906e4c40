<template>
  <View gap="md" direction="column">
    <TopBarFilters :filters="filters" :selectedFilter="selectedFilter" @selected="selectedFilter = $event" />
    <div class="applications">
      <ApplicationBundleCard
        v-for="applicationBundle in bundles"
        :key="applicationBundle.id"
        :applicationBundle="applicationBundle"
      />
    </div>
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';
import { TopBarFilters, View } from '@tallo/design-system';
import { ApplicationBundleCard, useApplicationBundles } from '@tallo/investor/applications';

enum ApplicationBundleFilter {
  READY_FOR_REVIEW = 'Ready for review',
  IN_PROGRESS = 'In progress',
}

const { applicationBundles } = useApplicationBundles();
const filters = Object.values(ApplicationBundleFilter);
const selectedFilter = ref(filters[0]);
const bundles = computed(() =>
  applicationBundles.value
    .filter((bundle) => {
      switch (selectedFilter.value) {
        case ApplicationBundleFilter.READY_FOR_REVIEW:
          return bundle.status === ApplicationBundleStatus.SUBMITTED;

        case ApplicationBundleFilter.IN_PROGRESS:
          return [ApplicationBundleStatus.SENT, ApplicationBundleStatus.IN_PROGRESS].includes(bundle.status);
      }
    })
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()),
);
</script>

<style scoped lang="scss">
.applications {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
