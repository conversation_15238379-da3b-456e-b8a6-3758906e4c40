<template>
  <View gap="md" direction="column">
    <TopBarFilters :filters="filters" :selectedFilter="selectedFilter" @selected="selectedFilter = $event" />

    <View v-if="isLoading" justify="center" align="center" padding="xl">
      <LoadingIndicator />
    </View>

    <View v-else-if="bundles.length === 0" justify="center" align="center" padding="xl" direction="column" gap="md">
      <Text variant="h3" align="center">No applications {{ getEmptyStateText() }}</Text>
      <Text variant="body-large" color="tertiary" align="center">
        {{ getEmptyStateDescription() }}
      </Text>
    </View>

    <div v-else class="applications">
      <ApplicationBundleCard
        v-for="applicationBundle in bundles"
        :key="applicationBundle.id"
        :applicationBundle="applicationBundle"
      />
    </div>
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';
import { LoadingIndicator, Text, TopBarFilters, View } from '@tallo/design-system';
import { ApplicationBundleCard } from '@tallo/investor/applications';

enum ApplicationBundleFilter {
  READY_FOR_REVIEW = 'Ready for review',
  IN_PROGRESS = 'In progress',
}

interface Props {
  isLoading: boolean;
  applicationBundles: any[];
}

const props = defineProps<Props>();

const filters = Object.values(ApplicationBundleFilter);
const selectedFilter = ref(filters[0]);

const bundles = computed(() =>
  props.applicationBundles
    .filter((bundle) => {
      switch (selectedFilter.value) {
        case ApplicationBundleFilter.READY_FOR_REVIEW:
          return bundle.status === ApplicationBundleStatus.SUBMITTED;

        case ApplicationBundleFilter.IN_PROGRESS:
          return [ApplicationBundleStatus.SENT, ApplicationBundleStatus.IN_PROGRESS].includes(bundle.status);
      }
    })
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()),
);

function getEmptyStateText(): string {
  switch (selectedFilter.value) {
    case ApplicationBundleFilter.READY_FOR_REVIEW:
      return 'ready for review';
    case ApplicationBundleFilter.IN_PROGRESS:
      return 'in progress';
    default:
      return 'available';
  }
}

function getEmptyStateDescription(): string {
  switch (selectedFilter.value) {
    case ApplicationBundleFilter.READY_FOR_REVIEW:
      return 'Applications that are ready for your review will appear here once renters submit them.';
    case ApplicationBundleFilter.IN_PROGRESS:
      return 'Applications that are currently being processed will appear here.';
    default:
      return 'Applications will appear here once renters start applying to your properties.';
  }
}
</script>

<style scoped lang="scss">
.applications {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
