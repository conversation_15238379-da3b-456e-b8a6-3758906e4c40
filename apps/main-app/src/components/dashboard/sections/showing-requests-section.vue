<template>
  <View v-if="isLoading" justify="center" align="center" padding="xl">
    <LoadingIndicator />
  </View>

  <View
    v-else-if="pendingShowings.length === 0"
    justify="center"
    align="center"
    padding="xl"
    direction="column"
    gap="md"
  >
    <Text variant="h3" align="center">No showing requests yet</Text>
    <Text variant="body-large" color="tertiary" align="center">
      When renters request showings for your properties, they'll appear here for you to review and schedule.
    </Text>
  </View>

  <div v-else class="showings">
    <ShowingCard v-for="showing in pendingShowings" :key="showing.id" :showing="showing" />
  </div>
</template>

<script setup lang="ts">
import { LoadingIndicator, Text, View } from '@tallo/design-system';
import { ShowingCard } from '@tallo/investor/showing';

interface Props {
  isLoading: boolean;
  pendingShowings: any[];
}

defineProps<Props>();
</script>

<style scoped lang="scss">
.showings {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
