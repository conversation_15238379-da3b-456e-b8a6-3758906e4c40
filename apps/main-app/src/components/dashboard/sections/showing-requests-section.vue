<template>
  <div class="showings">
    <ShowingCard v-for="showing in pendingShowings" :key="showing.id" :showing="showing" />
  </div>
</template>

<script setup lang="ts">
import { ShowingCard, useShowings } from '@tallo/investor/showing';

const { pendingShowings } = useShowings();
</script>

<style scoped lang="scss">
.showings {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
