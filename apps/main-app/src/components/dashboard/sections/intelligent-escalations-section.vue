<template>
  <div class="intelligent-escalations">
    <IntelligentEscalationCard
      v-for="escalation in intelligentEscalations"
      :key="escalation.id"
      :question="escalation"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { useIntelligentEscalationsQuery, IntelligentEscalationCard } from '@tallo/investor/intelligent-escalations';

const intelligentEscalationsQuery = useIntelligentEscalationsQuery();
const intelligentEscalations = computed(() => {
  const data = intelligentEscalationsQuery.data?.value || [];

  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
});
</script>

<style scoped lang="scss">
.intelligent-escalations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
