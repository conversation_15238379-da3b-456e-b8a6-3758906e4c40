import { PortfolioStatisticsDto } from '@tallo/investor/portfolio-statistics';

export enum StatisticsConfigName {
  LEADS = 'Leads',
  LISTINGS = 'Listings',
  SHOWINGS = 'Showings',
  APPLICATIONS = 'Applications',
}

type StatisticsConfigApiFieldKey = keyof PortfolioStatisticsDto;

export interface StatisticsConfigApiField {
  key: StatisticsConfigApiFieldKey;
  shortLabel: string;
  fullLabel: string;
}

export interface StatisticsConfig {
  name: StatisticsConfigName;
  apiFields: StatisticsConfigApiField[];
}

export const statisticsConfigs = new Map<StatisticsConfigName, StatisticsConfig>([
  [
    StatisticsConfigName.LEADS,
    {
      name: StatisticsConfigName.LEADS,
      apiFields: [
        {
          key: 'leads',
          shortLabel: 'Total',
          fullLabel: 'Total leads',
        },
        {
          key: 'qualifiedLeads',
          shortLabel: 'Qualified',
          fullLabel: 'Qualified leads',
        },
      ],
    },
  ],
  [
    StatisticsConfigName.LISTINGS,
    {
      name: StatisticsConfigName.LISTINGS,
      apiFields: [
        {
          key: 'listingsActive',
          shortLabel: 'Active',
          fullLabel: 'Active listings',
        },
        {
          key: 'listingsRentedOut',
          shortLabel: 'Rented out',
          fullLabel: 'Rented out listings',
        },
      ],
    },
  ],
  [
    StatisticsConfigName.SHOWINGS,
    {
      name: StatisticsConfigName.SHOWINGS,
      apiFields: [
        {
          key: 'showingsRequested',
          shortLabel: 'Requested',
          fullLabel: 'Requested showings',
        },
        {
          key: 'showingsCompleted',
          shortLabel: 'Completed',
          fullLabel: 'Completed showings',
        },
      ],
    },
  ],
  [
    StatisticsConfigName.APPLICATIONS,
    {
      name: StatisticsConfigName.APPLICATIONS,
      apiFields: [
        {
          key: 'applicationsSent',
          shortLabel: 'Sent',
          fullLabel: 'Sent applications',
        },
        {
          key: 'applicationsCompleted',
          shortLabel: 'Completed',
          fullLabel: 'Completed applications',
        },
      ],
    },
  ],
]);
