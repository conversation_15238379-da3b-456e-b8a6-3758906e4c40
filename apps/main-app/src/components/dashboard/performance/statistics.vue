<template>
  <div class="statistics">
    <View
      direction="row"
      class="statistics-item"
      :class="{ active: selectedStatisticsConfigName === statisticsItem.name }"
      @click="onSelectStatisticsItem(statisticsItem.name)"
      v-for="statisticsItem in statisticsItems"
      :key="statisticsItem.name"
    >
      <div class="indicator"></div>
      <View gap="sm" direction="column" :grow="1">
        <Text variant="h6" color="secondary">{{ statisticsItem.name }}</Text>
        <View gap="sm">
          <View gap="xs" direction="column" :grow="1" v-for="item in statisticsItem.items" :key="item.label">
            <Text variant="h2">{{ item.value }}</Text>
            <Text variant="label-small" color="tertiary" wrap="nowrap">{{ item.label }}</Text>
          </View>
        </View>
      </View>
    </View>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { Text, View } from '@tallo/design-system';
import { PortfolioStatisticsDto } from '@tallo/investor/portfolio-statistics';

import { statisticsConfigs, StatisticsConfigName } from './statistics-config';

const props = defineProps<{ data?: PortfolioStatisticsDto }>();
const selectedStatisticsConfigName = defineModel<StatisticsConfigName>({ required: true });
const data = computed(() => props.data);

interface StatisticsItem {
  name: StatisticsConfigName;
  items: { label: string; value: number }[];
}

const statisticsItems = computed<StatisticsItem[]>(() => {
  const configs = Array.from(statisticsConfigs.values());

  return configs.map((statisticsConfig) => {
    return {
      name: statisticsConfig.name,
      items: statisticsConfig.apiFields.map((apiField) => ({
        label: apiField.shortLabel,
        value: data.value?.[apiField.key] || 0,
      })),
    } as StatisticsItem;
  });
});

function onSelectStatisticsItem(item: StatisticsConfigName) {
  selectedStatisticsConfigName.value = item;
}
</script>

<style scoped lang="scss">
.statistics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2.5rem;
  overflow-x: auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.statistics-item {
  .indicator {
    width: 1rem;
    height: 100%;

    &::before {
      content: '';
      display: block;
      height: 100%;
      width: 1px;
      background-color: #e4e6e6;
    }
  }

  &.active .indicator::before {
    width: 3px;
    background-color: #f86a22;
  }

  &:hover {
    cursor: pointer;
  }
}
</style>
