<template>
  <View gap="sm" direction="column">
    <View gap="md" direction="row" justify="space-between">
      <Text variant="h3">Activity feed</Text>
      <Text
        as="button"
        variant="body-large"
        color="secondary"
        v-if="hasMoreDataThanLimit"
        class="see-all"
        @click="openModal"
      >
        See all
      </Text>
    </View>

    <ActivityFeedList :items="items" />

    <ActivityFeedModal ref="activityFeedModalRef" />
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { Text, View } from '@tallo/design-system';
import { useActivityFeedQuery, ActivityFeedModal, ActivityFeedList } from '@tallo/investor/activity-feed';

const activityFeedQuery = useActivityFeedQuery();
const data = computed(() => activityFeedQuery.data?.value || []);
const limit = 8;
const items = computed(() => data.value.slice(0, limit));
const hasMoreDataThanLimit = computed(() => data.value.length > limit);
const activityFeedModalRef = ref<InstanceType<typeof ActivityFeedModal>>();

function openModal() {
  activityFeedModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.see-all {
  appearance: none;
  border: none;
  background: none;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
