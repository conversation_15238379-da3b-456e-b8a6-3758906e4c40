<template>
  <View gap="sm" direction="column">
    <View gap="md" direction="row" justify="space-between">
      <Text variant="h3">
        <span>Actions needed</span>
        <Text variant="h3" as="span" color="tertiary">&nbsp;({{ data.length }})</Text>
      </Text>
      <Text
        as="button"
        variant="body-large"
        color="secondary"
        v-if="hasMoreDataThanLimit"
        class="see-all"
        @click="openModal"
      >
        See all
      </Text>
    </View>

    <ActionsNeededList :items="items" />
    <ActionsNeededModal ref="actionsNeededModalRef" />
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { Text, View } from '@tallo/design-system';
import { ActionsNeededList, ActionsNeededModal, useActionsNeeded } from '@tallo/investor/actions-needed';

const { data } = useActionsNeeded();
const limit = 2;
const items = computed(() => data.value.slice(0, limit));
const hasMoreDataThanLimit = computed(() => data.value.length > limit);
const actionsNeededModalRef = ref<InstanceType<typeof ActionsNeededModal>>();

function openModal() {
  actionsNeededModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.see-all {
  appearance: none;
  border: none;
  background: none;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
