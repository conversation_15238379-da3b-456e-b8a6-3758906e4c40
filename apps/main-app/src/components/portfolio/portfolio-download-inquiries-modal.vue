<template>
  <Modal ref="modalRef" width="520px">
    <template #header>Download portfolio inquiries</template>

    <template #body>
      <FormField label="Choose period" style="width: 100%">
        <Select v-model="selectedFilter">
          <option v-for="filter in timeFilters" :key="filter" :value="filter">{{ filter }}</option>
        </Select>
      </FormField>
    </template>

    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <Button :loading="isDownloading" :disabled="isDownloading || !companyStore.company?.id" @click="download">
        Download
      </Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

import { Button, FormField, Modal, Select, useAlertsStore } from '@tallo/design-system';
import { useCompanyStore } from '@tallo/investor/company';
import { propertyStatisticsService } from '@tallo/investor/property';
import { TimeFilter, timeFilters, downloadFile, generateStatsFilename } from '@tallo/utility';

const modalRef = ref<InstanceType<typeof Modal> | null>(null);
const companyId = computed(() => companyStore.company?.id);
const isDownloading = ref(false);
const selectedFilter = ref<TimeFilter>(TimeFilter.LAST_7_DAYS);
const alertsStore = useAlertsStore();
const companyStore = useCompanyStore();

async function open() {
  // Ensure company is loaded before interacting with the modal
  if (!companyStore.isLoaded && !companyStore.isLoading) {
    try {
      await companyStore.load();
    } catch (e) {
      console.error('Failed to load company for download modal:', e);
    }
  }
  modalRef.value?.show();
}

function close() {
  modalRef.value?.close();
}

async function download() {
  try {
    isDownloading.value = true;

    if (!companyId) {
      alertsStore.error('Company not loaded');
      return;
    }

    const blob = await propertyStatisticsService.downloadCompanyStatistics(companyId.value!, selectedFilter.value);
    const filename = generateStatsFilename('leads', selectedFilter.value);
    downloadFile(blob, filename);
    alertsStore.success('Download started');
    close();
  } catch (e) {
    console.error(e);
    alertsStore.error('Failed to download. Please try again.');
  } finally {
    isDownloading.value = false;
  }
}

defineExpose({ open, close, modalRef });
</script>
