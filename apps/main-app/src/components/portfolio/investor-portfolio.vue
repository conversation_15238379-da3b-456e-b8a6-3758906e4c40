<template>
  <PageContainer>
    <View gap="lg" direction="column">
      <View gap="md" justify="space-between" class="page-header">
        <Text variant="h1" class="title">Your portfolio</Text>
        <View gap="sm" align="end">
          <Button variant="outline" size="medium" :to="newListingRouteTo">
            <Icon name="plus" />
            <span>Add new listing</span>
          </Button>

          <Button variant="outline" size="medium" @click="openDownloadModal">
            <Icon name="download-cloud-02" size="medium" />
            <span>Download</span>
          </Button>
        </View>
      </View>

      <View direction="column" gap="xl" padding-top="sm">
        <TopBarFilters :selected-filter="selectedFilter" :filters="filters" @selected="filterProperties" />

        <View v-if="state.status === 'pending'" justify="center" padding="xl">
          <LoadingIndicator />
        </View>

        <EmptyPagePlaceholder
          v-else-if="state.status === 'error'"
          title="Could not load portfolio"
          description="Please reload the page or try again later"
        />

        <template v-else>
          <EmptyPagePlaceholder
            v-if="filteredProperties.length === 0"
            title="No properties found"
            description="Adjust your filters to find what you're looking for"
          />

          <div v-else class="property-cards-wrapper">
            <PropertyCard v-for="property in filteredProperties" :key="property.id" :property="property" />
          </div>
        </template>
      </View>
    </View>
    <PortfolioDownloadInquiriesModal ref="downloadModalRef" />
  </PageContainer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import {
  Button,
  Card,
  EmptyPagePlaceholder,
  IconButton,
  Icon,
  LoadingIndicator,
  PageContainer,
  Text,
  TopBarFilters,
  View,
} from '@tallo/design-system';
import { AppRouteNames, PropertyOnboardingType } from '@tallo/investor/navigation';
import { PropertyCard, PropertyWithPendingEventsDto, usePortfolioQuery } from '@tallo/investor/property';
import { PropertyStatus } from '@tallo/property';

import PortfolioDownloadInquiriesModal from './portfolio-download-inquiries-modal.vue';

enum PortfolioFilter {
  ALL = 'All',
  LISTED = 'Listed',
  LISTING_IN_PROGRESS = 'Listing in progress',
  READY_FOR_LISTING = 'Ready for listing',
  DRAFT = 'Draft',
  UNLISTED = 'Unlisted',
  RENTED_OUT = 'Rented out',
}

const selectedFilter = ref(PortfolioFilter.ALL);
const downloadModalRef = ref<InstanceType<typeof PortfolioDownloadInquiriesModal> | null>(null);

function openDownloadModal() {
  downloadModalRef.value?.open();
}
const filters = Object.values(PortfolioFilter);
const { properties, state } = usePortfolioQuery();

const newListingRouteTo = {
  name: AppRouteNames.INVESTOR_PROPERTY_ONBOARDING_OVERVIEW,
  params: { propertyId: PropertyOnboardingType.NewProperty },
};

const filteredProperties = computed(() => {
  switch (selectedFilter.value) {
    case PortfolioFilter.LISTED:
      return properties.value.filter((property) => property.status === PropertyStatus.LISTED);

    case PortfolioFilter.LISTING_IN_PROGRESS:
      return properties.value.filter((property) => property.status === PropertyStatus.LISTING_IN_PROGRESS);

    case PortfolioFilter.READY_FOR_LISTING:
      return properties.value.filter((property) => property.status === PropertyStatus.READY_FOR_LISTING);

    case PortfolioFilter.DRAFT:
      return properties.value.filter((property) => property.status === PropertyStatus.DRAFT);

    case PortfolioFilter.UNLISTED:
      return properties.value.filter((property) => property.status === PropertyStatus.UNLISTED);

    case PortfolioFilter.RENTED_OUT:
      return properties.value.filter((property) => property.status === PropertyStatus.RENTED_OUT);

    default:
      return sortPropertiesByStatus(properties.value);
  }
});

function filterProperties(filter: PortfolioFilter) {
  selectedFilter.value = filter;
}

function sortPropertiesByStatus(properties: PropertyWithPendingEventsDto[]): PropertyWithPendingEventsDto[] {
  return properties.sort((a, b) => {
    const priorityOrder = {
      [PropertyStatus.LISTED]: 1,
      [PropertyStatus.LISTING_IN_PROGRESS]: 2,
      [PropertyStatus.READY_FOR_LISTING]: 3,
      [PropertyStatus.DRAFT]: 4,
      [PropertyStatus.UNLISTED]: 5,
      [PropertyStatus.RENTED_OUT]: 6,
    };

    const priorityA = priorityOrder[a.status];
    const priorityB = priorityOrder[b.status];

    return priorityA - priorityB;
  });
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.page-header {
  @include screen-max-sm {
    flex-direction: column;
  }
}

.property-cards-wrapper {
  display: grid;
  gap: var(--t-spacing-sm);
  grid-template-columns: repeat(auto-fill, minmax(22rem, 1fr));

  @include screen-max-sm {
    grid-template-columns: 1fr;
  }
}
</style>
