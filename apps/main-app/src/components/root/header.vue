<template>
  <header class="header-wrapper">
    <PageContainer height="full" class="header">
      <div :class="{ 'teleport-hidden': teleportIsNotUsed }" :id="headerTeleportSlot" ref="teleportSlot"></div>

      <router-link v-if="teleportIsNotUsed" to="/" class="logo-link">
        <Logo />
      </router-link>

      <template v-if="navigationLinksVisible">
        <NavPanel class="nav-desktop" variant="primary" v-if="hasActiveProperties" :links="mainNavigationConfig" />

        <div class="nav-desktop">
          <ProfileNavItem class="profile-nav-item" />
        </div>

        <div class="nav-mobile">
          <IconButton @click="openMenu" variant="ghost" icon="menu-01" />
        </div>

        <BurgerMenu @logout="$emit('logout')" ref="menuDialog" />
      </template>
    </PageContainer>
  </header>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, nextTick, onMounted, onUnmounted, onUpdated, ref } from 'vue';
import { useRouter } from 'vue-router';

import { IconButton, Logo, PageContainer } from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { mainNavigationConfig } from '@tallo/investor/navigation';
import { usePortfolioQuery } from '@tallo/investor/property';

import BurgerMenu from './burger-menu.vue';
import { headerTeleportSlot } from './header-teleport-slot.const';
import NavPanel from './nav-panel/nav-panel.vue';
import ProfileNavItem from './nav-panel/profile-nav-item.vue';

defineEmits(['logout']);

const { hasActiveProperties } = usePortfolioQuery();
const investorStore = useInvestorStore();
const { needsInitialOnboarding } = storeToRefs(investorStore);

const menuDialog = ref<InstanceType<typeof BurgerMenu>>();
const openMenu = () => menuDialog.value?.open();

const router = useRouter();

const navigationLinksVisible = computed(() => {
  return !needsInitialOnboarding.value;
});

const teleportSlot = ref<HTMLElement | null>(null);
const teleportIsNotUsed = ref(true);
let clearAfterNavigationCallback: () => void;

function checkIfTeleportIsUsed(): void {
  teleportIsNotUsed.value = teleportSlot.value?.children !== undefined && teleportSlot.value.children.length === 0;
}

onUpdated(() => {
  checkIfTeleportIsUsed();
});

onMounted(() => {
  checkIfTeleportIsUsed();

  window.addEventListener('resize', checkIfTeleportIsUsed);
  clearAfterNavigationCallback = router.afterEach(() => {
    nextTick(() => checkIfTeleportIsUsed()); // Wait for teleport to be used after navigation
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', checkIfTeleportIsUsed);
  clearAfterNavigationCallback();
});
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.header-wrapper {
  flex-shrink: 0;
  height: var(--t-header-height);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-link {
  line-height: 1;
}

.profile-nav-item {
  margin-left: var(--t-spacing-md);
}

.nav-mobile {
  display: none;
}

.teleport-hidden {
  display: none;
}

@include mobile {
  .header-wrapper {
    height: var(--t-header-height-mobile);
  }

  .nav-mobile {
    display: block;
  }

  .nav-desktop {
    display: none;
  }
}
</style>
