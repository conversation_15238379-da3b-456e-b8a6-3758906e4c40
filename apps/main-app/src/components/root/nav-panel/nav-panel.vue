<template>
  <div class="nav-panel" :class="{ compact: variant === 'secondary' }">
    <router-link
      class="link"
      v-for="link of links"
      :key="link.appRoute"
      :id="link.id"
      :to="{
        name: link.appRoute,
        params: link.params,
      }"
    >
      <Text class="text" :variant="variant === 'primary' ? 'label-large' : 'label-medium'" as="span">
        {{ link.title }}
      </Text>
      <div class="bottom-line"></div>
    </router-link>
  </div>
</template>

<script setup lang="ts" generic="T">
import { Text } from '@tallo/design-system';

import { NavPanelLink } from './nav-panel-link.interface';

withDefaults(
  defineProps<{
    links: NavPanelLink[];
    variant?: 'primary' | 'secondary';
  }>(),
  {
    variant: 'primary',
  },
);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.nav-panel {
  display: flex;
  overflow: auto;
  height: 3.5rem;
  gap: var(--t-spacing-xl);

  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &.compact {
    height: 3.5rem;
  }
}

.link {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  color: var(--t-text-color-tertiary);
  text-wrap: nowrap;

  &.router-link-active {
    color: var(--t-text-color-primary);

    .text {
      margin-top: auto;
      margin-bottom: -0.125rem;
    }

    .bottom-line {
      width: 100%;
      height: 0.125rem;
      border-radius: var(--t-border-radius-xs);
      background-color: var(--t-text-color-primary);
      margin-top: auto;
    }
  }
}
</style>
