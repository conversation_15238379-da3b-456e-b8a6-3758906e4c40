<template>
  <router-link class="link" :to="profileRoute()">
    <View align="center" justify="center" class="avatar-container">
      <Avatar
        class="avatar"
        v-if="investor?.user"
        :userName="investor.user.name"
        size="large"
        :showSingleLetter="true"
      />
    </View>
  </router-link>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';

import { Avatar, View } from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { profileRoute } from '@tallo/investor/navigation';

const investorStore = useInvestorStore();
const { investor } = storeToRefs(investorStore);
</script>

<style scoped lang="scss">
.link {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  color: var(--t-text-color-tertiary);
  text-wrap: nowrap;

  &.router-link-active {
    color: var(--t-text-color-primary);

    .avatar-container {
      margin-top: auto;
      margin-bottom: -0.125rem;
    }
  }
}

.avatar-container {
  cursor: pointer;
}

.avatar {
  background-color: #f66b22 !important;
  color: #fff;
}
</style>
