<template>
  <PageContainer>
    <View gap="lg" direction="column">
      <View gap="md" justify="space-between">
        <Text variant="h1">Calendar</Text>
        <ScrollableHorizontallyControls :scrollable-container="scrollableContainerRef" />
      </View>

      <View class="calendar">
        <Card size="medium" class="card" variant="outline" :height="'fit-content'">
          <template #header>
            <View direction="column" gap="xs">
              <Text variant="h4">Connect your calendar</Text>
              <Text variant="body-medium" color="tertiary" class="calendar-text">
                Add your Google Calendar to link the confirmed showings and let Tallo schedule future showings, avoiding
                any conflicts with existing events
              </Text>
            </View>
          </template>

          <View direction="column" gap="md" padding-top="sm">
            <GoogleCalendarIntegration />
          </View>
        </Card>
      </View>

      <View v-if="state.status === 'pending'" justify="center" padding="xl">
        <LoadingIndicator />
      </View>

      <EmptyPagePlaceholder
        v-else-if="state.status === 'error'"
        title="Could not load showings"
        description="Please reload the page or try again later"
      />

      <template v-else>
        <EmptyPagePlaceholder
          v-if="groupedByDateShowings.length === 0"
          :title="'You don\'t have any showings yet'"
          :description="'Tallo is working to schedule showings. Once there is activity, you can see everything related to showings here'"
        />

        <ScrollableHorizontallyContainer v-else ref="scrollableContainerRef">
          <View gap="sm" padding-top="md" class="showings">
            <View
              gap="sm"
              direction="column"
              v-for="showingsOnDate in groupedByDateShowings"
              :key="showingsOnDate.date.getTime()"
            >
              <View gap="sm">
                <Text variant="h4">{{ useDateFormat(showingsOnDate.date, 'MMM D') }}</Text>
                <Text variant="body-large" color="tertiary">
                  {{ useDateFormat(showingsOnDate.date, 'dddd') }}
                </Text>
              </View>

              <View gap="sm" direction="column">
                <ShowingCard
                  class="showing"
                  v-for="showing in showingsOnDate.showings"
                  :key="showing.id"
                  :showing="showing"
                  :show-status="false"
                />
              </View>
            </View>
          </View>
        </ScrollableHorizontallyContainer>
      </template>
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import { computed, ref } from 'vue';

import {
  Card,
  PageContainer,
  Text,
  View,
  EmptyPagePlaceholder,
  ScrollableHorizontallyContainer,
  ScrollableHorizontallyControls,
  LoadingIndicator,
} from '@tallo/design-system';
import { groupShowingByDateAndSort, ShowingCard, ShowingStatus, useShowingsQuery } from '@tallo/investor/showing';

import GoogleCalendarIntegration from './profile/integrations/google/google-calendar-integration.vue';

const { state } = useShowingsQuery();
const scrollableContainerRef = ref();

const groupedByDateShowings = computed(() => {
  const showings = (state.value.data || []).filter(({ status }) => status === ShowingStatus.CONFIRMED);

  return groupShowingByDateAndSort(showings);
});
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.calendar,
.card {
  width: 100%;
}

.calendar-text {
  max-width: 34rem;
}

.showings {
  @include mobile {
    flex-direction: column;
    gap: var(--t-spacing-lg);
  }

  .showing {
    min-width: 23rem;

    @include mobile {
      min-width: 100%;
    }
  }
}
</style>
