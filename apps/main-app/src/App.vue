<template>
  <div id="app">
    <template v-if="isAuthenticated">
      <div class="header">
        <Header />
        <RouterView name="subheader" />
      </div>
    </template>

    <div class="main-router-view" :class="{ authenticated: isAuthenticated }">
      <RouterView />
    </div>

    <Alerts />
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useAuthStore } from '@tallo/auth';
import { Alerts } from '@tallo/design-system';

import Header from './components/root/header.vue';
import appInitService from './services/app-init.service';

const authStore = useAuthStore();
const router = useRouter();
const { isAuthenticated } = storeToRefs(authStore);

onMounted(() => {
  authStore.setupAuthenticatedFlag();
  authStore.initializeGoogleSignInClient();
  appInitService.setRouter(router);
});

watch(isAuthenticated, async () => {
  if (isAuthenticated.value) {
    await appInitService.initApp();
  } else {
    appInitService.reset();
  }
});
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

#app {
  display: flex;
  flex-direction: column;
  height: 100%;

  .header {
    padding-inline: var(--t-spacing-md);

    @include tablet-start {
      padding-inline: var(--t-spacing-lg);
    }

    @include desktop {
      padding-inline: var(--t-spacing-xl);
    }
  }

  .main-router-view {
    flex: 1 0 auto;

    &.authenticated {
      flex: 1;
      width: 100%;
      padding: var(--t-spacing-md);

      @include tablet-start {
        padding: var(--t-spacing-lg);
      }

      @include desktop {
        padding: var(--t-spacing-xl);
      }
    }
  }
}
</style>
