<template>
  <div class="scroll-wrapper">
    <div class="top-bar-filters-component">
      <button
        v-for="filter of filters"
        :key="filter?.toString()"
        class="filter"
        :class="{ selected: filter === selectedFilter }"
        @click="selectFilter(filter)"
      >
        <Text variant="label-medium">{{ filter }}</Text>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import Text from './text.vue';

defineProps<{
  filters: T[];
  selectedFilter: T;
}>();

const emit = defineEmits<{ selected: [filter: T] }>();

function selectFilter(filter: T) {
  emit('selected', filter);
}
</script>

<style scoped lang="scss">
.scroll-wrapper {
  overflow: hidden; /* Hide the overflow */
  max-width: 100%; /* Adjust this as needed */
}

.top-bar-filters-component {
  display: flex;
  overflow-x: auto;
  gap: var(--t-spacing-sm);
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.filter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--t-spacing-md);

  font-size: 0.875rem;
  font-weight: 500;

  line-height: 1.25rem;
  padding: 0.5rem;
  width: fit-content;
  height: 2rem;
  white-space: nowrap;
  border: none;
  background: transparent;
  appearance: none;
  color: #84857c;
  border: 1px solid transparent;
  border-radius: var(--t-border-radius-sm);

  &:hover,
  &:focus {
    cursor: pointer;
    background: #ebebe9;
    border-color: #ebebe9;
  }

  &:focus {
    border-color: var(--t-color-neutral-900);
    outline: none;
  }

  &.selected {
    background: #ebebe9;
    border-color: #ebebe9;
    color: var(--t-text-color-primary);
  }
}
</style>
