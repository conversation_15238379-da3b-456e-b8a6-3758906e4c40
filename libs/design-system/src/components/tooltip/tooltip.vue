<template>
  <div ref="triggerRef" class="tooltip-wrapper">
    <!-- Custom trigger slot -->
    <div
      v-if="$slots.trigger"
      class="tooltip-trigger"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
      :tabindex="effectiveTrigger === 'focus' ? 0 : undefined"
    >
      <slot name="trigger" />
    </div>

    <!-- Default icon trigger -->
    <Icon
      v-else
      :name="icon"
      :size="iconSize"
      class="tooltip-trigger tooltip-icon"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
      :tabindex="effectiveTrigger === 'focus' ? 0 : undefined"
    />

    <!-- Tooltip content -->
    <div
      v-if="isVisible"
      ref="tooltipRef"
      class="tooltip-content"
      :style="floatingStyles"
      role="tooltip"
      :aria-hidden="!isVisible"
    >
      <Text variant="label-small" color="primary" class="tooltip-text">
        {{ content }}
      </Text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { autoUpdate, flip, offset, shift, size, useFloating } from '@floating-ui/vue';
import { computed, ref, useTemplateRef } from 'vue';

import { screenXs } from '../../utils/tallo-breakpoints.util';
import { IconName } from '../../values/icons.const';
import Icon from '../icon.vue';
import Text from '../text.vue';

type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end';
type TooltipTrigger = 'hover' | 'click' | 'focus';

interface TooltipProps {
  /**
   * The content to display in the tooltip
   */
  content: string;

  /**
   * Placement of the tooltip relative to the trigger element
   * @default 'bottom'
   */
  placement?: TooltipPlacement;

  /**
   * How the tooltip is triggered
   * @default 'hover'
   */
  trigger?: TooltipTrigger;

  /**
   * Icon to display as the trigger element
   * @default 'question-mark-circle'
   */
  icon?: IconName;

  /**
   * Size of the trigger icon
   * @default 'small'
   */
  iconSize?: 'large' | 'medium' | 'small' | 'extra-small';

  /**
   * Delay in milliseconds before showing the tooltip on hover
   * @default 200
   */
  showDelay?: number;

  /**
   * Delay in milliseconds before hiding the tooltip on hover
   * @default 100
   */
  hideDelay?: number;

  /**
   * Whether the tooltip is disabled
   * @default false
   */
  disabled?: boolean;
}

const props = withDefaults(defineProps<TooltipProps>(), {
  placement: 'bottom',
  trigger: 'hover',
  icon: 'question-mark-circle',
  iconSize: 'small',
  showDelay: 200,
  hideDelay: 100,
  disabled: false,
});

const effectiveTrigger = computed(() => {
  return screenXs.value && props.trigger === 'hover' ? 'click' : props.trigger;
});

const isVisible = ref(false);
const showTimeout = ref<ReturnType<typeof setTimeout> | null>(null);
const hideTimeout = ref<ReturnType<typeof setTimeout> | null>(null);

const triggerRef = useTemplateRef<HTMLElement>('triggerRef');
const tooltipRef = useTemplateRef<HTMLElement>('tooltipRef');

const { floatingStyles } = useFloating(triggerRef, tooltipRef, {
  placement: props.placement,
  strategy: 'fixed',
  whileElementsMounted: autoUpdate,
  middleware: [
    offset(8),
    flip({
      fallbackPlacements: ['top', 'bottom', 'left', 'right'],
    }),
    shift({
      padding: 8,
      crossAxis: true,
    }),
    size({
      apply({ availableWidth, elements }) {
        Object.assign(elements.floating.style, {
          maxWidth: `min(320px, ${Math.floor(availableWidth * 0.8)}px)`,
        });
      },
    }),
  ],
});

function clearTimeouts() {
  if (showTimeout.value) {
    clearTimeout(showTimeout.value);
    showTimeout.value = null;
  }
  if (hideTimeout.value) {
    clearTimeout(hideTimeout.value);
    hideTimeout.value = null;
  }
}

function show() {
  if (props.disabled) return;

  clearTimeouts();
  showTimeout.value = setTimeout(() => {
    isVisible.value = true;
  }, props.showDelay);
}

function hide() {
  clearTimeouts();
  hideTimeout.value = setTimeout(() => {
    isVisible.value = false;
  }, props.hideDelay);
}

function handleMouseEnter() {
  if (effectiveTrigger.value === 'hover') {
    show();
  }
}

function handleMouseLeave() {
  if (effectiveTrigger.value === 'hover') {
    hide();
  }
}

function handleClick() {
  if (effectiveTrigger.value === 'click') {
    if (isVisible.value) {
      hide();
    } else {
      show();
    }
  }
}

function handleFocus() {
  if (effectiveTrigger.value === 'focus') {
    show();
  }
}

function handleBlur() {
  if (effectiveTrigger.value === 'focus') {
    hide();
  }
}
</script>

<style scoped lang="scss">
.tooltip-wrapper {
  position: relative;
  display: inline-block;
}

@media (max-width: 30rem) {
  .tooltip-content {
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
  }
}

.tooltip-trigger {
  cursor: help;

  &:focus {
    outline: 2px solid var(--t-color-blue-500);
    outline-offset: 2px;
    border-radius: var(--t-border-radius-xs);
  }
}

.tooltip-icon {
  color: var(--t-color-neutral-500);
  transition: color 50ms ease-in-out;

  &:hover {
    color: var(--t-color-neutral-700);
  }
}

.tooltip-content {
  position: fixed;
  z-index: 1000;
  width: max-content;
  padding: var(--t-spacing-sm) var(--t-spacing-md);
  background: var(--t-color-neutral-900);
  border-radius: var(--t-border-radius-sm);
  box-shadow: var(--t-shadow-md);
  pointer-events: none;
  overscroll-behavior: contain;

  .tooltip-text {
    color: var(--t-color-neutral-white);
    line-height: 1.4;
  }
}
</style>
