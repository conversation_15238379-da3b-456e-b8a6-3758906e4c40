<template>
  <div class="page-container" :class="[heightClass, size]">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = withDefaults(defineProps<{ height?: 'auto' | 'full'; size?: 'large' | 'medium' }>(), {
  height: 'auto',
  size: 'large',
});
const heightClass = computed(() => `height-${props.height}`);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.page-container {
  width: 100%;
  margin: 0 auto;

  &.large {
    max-width: 86rem;
  }

  &.medium {
    max-width: 40rem;
  }

  &.height-full {
    height: 100%;
  }
}
</style>
