<template>
  <RouterLink v-if="props.to" :to="props.to" :class="buttonClass">
    <Icon :name="icon" :size="iconSize" />
  </RouterLink>

  <button v-else :class="buttonClass" :disabled="disabled">
    <Icon :name="icon" :size="iconSize" />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { RouteLocationRaw } from 'vue-router';

import { IconName } from '../values/icons.const';
import Icon from './icon.vue';

export type IconButtonSize = 'small' | 'medium' | 'large';
export type IconButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type IconButtonShape = 'round' | 'square';

interface IconButton {
  size?: IconButtonSize;
  variant?: IconButtonVariant;
  shape?: IconButtonShape;
  disabled?: boolean;
  to?: RouteLocationRaw;
  icon: IconName;
}

const props = withDefaults(defineProps<IconButton>(), {
  size: 'large',
  variant: 'primary',
  shape: 'square',
  disabled: false,
});

const iconSize = computed(() => (props.size === 'small' ? 'extra-small' : undefined));
const buttonClass = computed(() => ['button', props.size, props.variant, props.shape]);
</script>

<style scoped lang="scss">
@function get-size($size: large) {
  @return map-get(
    (
      small: 1.75rem,
      medium: 2.5rem,
      large: 3rem,
    ),
    $size
  );
}

.button {
  appearance: none;
  touch-action: manipulation;

  display: inline-flex;
  justify-content: center;
  align-items: center;

  width: auto;
  height: fit-content;
  padding: 0;

  border: none;
  background: transparent;
  cursor: pointer;

  transition-property: background-color, color, border-color;
  transition-duration: 150ms;
  transition-timing-function: ease-in-out;

  &:disabled {
    cursor: not-allowed;
  }

  &.primary {
    color: var(--t-color-neutral-white);
    background-color: var(--t-color-neutral-800);
    border: 1px solid var(--t-color-neutral-800);

    &:hover:not(:disabled) {
      background-color: var(--t-color-neutral-900);
      border: 1px solid var(--t-color-neutral-900);
    }
  }

  &.secondary {
    color: var(--t-color-neutral-900);
    background-color: var(--t-color-neutral-200);
    border: 1px solid var(--t-color-neutral-200);

    &:hover:not(:disabled) {
      background-color: var(--t-color-neutral-200);
      border: 1px solid var(--t-color-neutral-800);
    }
  }

  &.outline {
    color: var(--t-color-neutral-900);
    background-color: transparent;
    border: 1px solid var(--t-color-neutral-400);

    &:hover:not(:disabled) {
      background-color: transparent;
      border: 1px solid var(--t-color-neutral-700);
    }
  }

  &.ghost {
    color: var(--t-color-neutral-900);
    background-color: transparent;
    border: 1px solid transparent;

    &:hover:not(:disabled) {
      background-color: var(--t-color-neutral-100);
      border: 1px solid var(--t-color-neutral-200);
    }
  }

  &.small {
    width: get-size(small);
    height: get-size(small);

    &.square {
      border-radius: var(--t-border-radius-xs);
    }
  }

  &.medium {
    width: get-size(medium);
    height: get-size(medium);

    &.square {
      border-radius: var(--t-border-radius-sm);
    }
  }

  &.large {
    width: get-size(large);
    height: get-size(large);

    &.square {
      border-radius: var(--t-border-radius-sm);
    }
  }

  &.round {
    border-radius: var(--t-border-radius-half-rounded);
  }

  &:disabled {
    color: var(--t-color-neutral-500);
  }
}
</style>
