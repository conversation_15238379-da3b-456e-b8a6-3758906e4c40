<template>
  <Teleport to="#modals">
    <Transition
      name="modal"
      :duration="200"
      @before-enter="emit('beforeShow')"
      @after-enter="emit('afterShow')"
      @before-leave="emit('beforeClose')"
      @after-leave="emit('afterClose')"
    >
      <UseFocusTrap
        class="modal-container"
        v-if="isVisible"
        tabindex="-1"
        @keydown.esc="close"
        :options="{ allowOutsideClick: true }"
      >
        <div class="modal-overlay" @click="close"></div>
        <div class="modal-content" :class="props.position" :style="{ width: props.width }">
          <div class="modal-header">
            <div class="header-title">
              <slot name="header"></slot>
            </div>
            <IconButton
              class="close-button"
              variant="ghost"
              size="medium"
              icon="x-close"
              aria-label="close"
              @click="close"
            />
          </div>
          <div class="modal-body" :class="{ frameless: props.framelessBody }">
            <slot name="body"></slot>
          </div>
          <div class="modal-footer" v-if="$slots.footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </UseFocusTrap>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { UseFocusTrap } from '@vueuse/integrations/useFocusTrap/component';
import { onUnmounted, ref } from 'vue';

import IconButton from './icon-button.vue';

interface ModalProps {
  position?: 'center' | 'end';
  width?: string;
  framelessBody?: boolean;
}

const isVisible = ref(false);
const props = withDefaults(defineProps<ModalProps>(), {
  position: 'center',
  framelessBody: false,
});

const emit = defineEmits(['beforeShow', 'afterShow', 'beforeClose', 'afterClose']);

function show(): void {
  isVisible.value = true;
}

function close(): void {
  isVisible.value = false;
}

onUnmounted(() => close());

defineExpose({
  show,
  close,
  isVisible,
});
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.modal-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;

  background: var(--t-color-neutral-white);

  &.center {
    max-width: 90%;
    max-height: 90%;
    border-radius: var(--t-border-radius-md);

    .modal-header {
      padding: var(--t-spacing-sm) var(--t-spacing-md);
    }
  }

  &.end {
    width: 520px;
    height: 100%;
    margin-inline-start: auto;
  }
}

.modal-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: var(--t-spacing-sm);
  word-break: break-word;

  padding: var(--t-spacing-md) var(--t-spacing-md) var(--t-spacing-md) var(--t-spacing-lg);
  border-bottom: 1px solid var(--t-color-neutral-200);

  color: var(--t-color-neutral-900);
  font-weight: bold;

  &:has(> .header-title:empty) {
    border-bottom: none;
  }
}

.modal-body {
  flex-grow: 1;
  overflow-x: auto;
  overscroll-behavior: contain;
  padding: var(--t-spacing-lg);

  @include mobile {
    padding: var(--t-spacing-md);
  }

  &.frameless {
    padding: 0 !important;
  }
}

.modal-footer:not(:empty) {
  display: flex;
  justify-content: space-between;
  gap: var(--t-spacing-sm);
  padding: var(--t-spacing-lg);

  border-top: 1px solid var(--t-color-neutral-200);
}

.modal-footer > :last-child {
  margin-inline-start: auto;
}

.modal-enter-active,
.modal-leave-active {
  --modal-animation-duration: 200ms;

  .modal-overlay {
    transition: opacity var(--modal-animation-duration) ease-out;
  }

  .modal-content.center {
    transition: all var(--modal-animation-duration) ease-out;
  }

  .modal-content.end {
    transition: all var(--modal-animation-duration) ease-in-out;
  }
}

.modal-leave-to,
.modal-enter-from {
  .modal-overlay {
    opacity: 0;
  }

  .modal-content.end {
    transform: translateX(100%);
  }
}

.modal-leave-to,
.modal-enter-from {
  .modal-content.center {
    opacity: 0;
    transform: translateY(-30px);
  }
}

@include mobile {
  .modal-content {
    &.end,
    &.center {
      width: 100% !important;
      max-width: 100%;
      max-height: 95%;
      align-self: end;
      height: fit-content;
      border-radius: var(--t-border-radius-md) 0.75rem 0 0;

      .modal-header {
        padding: var(--t-spacing-sm);

        &:before {
          content: '';
          width: 2.5rem;
        }
      }

      .modal-footer {
        padding: var(--t-spacing-md);
      }
    }
  }

  .modal-leave-to,
  .modal-enter-from {
    .modal-content {
      &.center,
      &.end {
        transform: translate(0, 100%);
      }
    }
  }
}
</style>
