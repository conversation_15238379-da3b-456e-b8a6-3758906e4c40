<template>
  <component
    :is="as"
    class="card"
    :class="[
      { actionable: props.actionable, gap: !!$slots.header && !!$slots.default },
      props.variant,
      props.size,
      heightClass,
    ]"
  >
    <div class="card-header" v-if="$slots.header">
      <slot name="header" />
    </div>

    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { CardProps } from './card-props.interface';

const props = withDefaults(defineProps<CardProps>(), {
  as: 'div',
  variant: 'primary',
  size: 'large',
  actionable: false,
  height: 'auto',
});

const heightClass = computed(() => `height-${props.height}`);
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.card {
  padding: var(--t-spacing-lg);
  transition: all 250ms;
  border-radius: var(--t-border-radius-lg);

  &.primary {
    background-color: var(--t-color-neutral-white);
  }

  &.secondary {
    background-color: var(--t-color-neutral-white);
    box-shadow: var(--t-shadow-sm);
  }

  &.tertiary {
    background-color: #f7f8f8;
  }

  &.outline {
    background-color: var(--t-color-neutral-white);
    border: 1px solid var(--t-color-neutral-300);
  }

  &.outline-secondary {
    background-color: var(--t-color-neutral-100);
    border: 1px solid var(--t-color-neutral-300);
  }

  &.small {
    padding: 0.75rem;
  }

  &.medium {
    padding: var(--t-spacing-md);
  }

  &.large {
    padding: var(--t-spacing-lg);
  }

  &.extra-large {
    padding: 2.5rem;
  }

  &.large,
  &.extra-large {
    @include mobile {
      padding: var(--t-spacing-md);
    }
  }

  &.gap {
    & > .card-header {
      margin-bottom: var(--t-spacing-md);
    }

    &.medium > .card-header {
      margin-bottom: var(--t-spacing-sm);
    }
  }

  &.actionable:hover {
    box-shadow: var(--t-shadow-md);
  }

  &.height-full {
    min-height: 100%;
  }

  &.height-fit-content {
    height: fit-content;
  }

  @include mobile {
    // &.primary {
    //   border-radius: var(--t-border-radius-none);
    // }

    .action {
      opacity: 1;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}
</style>
