<template>
  <component :is="componentName" :class="[variant, `color-${color}`, `align-${align}`, textWrap]">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';

type TextVariant =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'body-large'
  | 'body-medium'
  | 'body-small'
  | 'label-large'
  | 'label-medium'
  | 'label-small'
  | 'inherit';

interface TextProps {
  variant: TextVariant;
  as?: HTMLElementTagNameMap | (string & {});
  color?: 'primary' | 'secondary' | 'tertiary' | 'inherit';
  align?: 'start' | 'center' | 'end' | 'inherit';
  wrap?: 'wrap' | 'nowrap' | 'balance' | 'pretty' | 'inherit';
}

defineOptions({
  inheritAttrs: true,
});

const props = withDefaults(defineProps<TextProps>(), {
  color: 'inherit',
  align: 'inherit',
});

const variantTags = new Map<TextVariant, string>([
  ['h1', 'h1'],
  ['h2', 'h2'],
  ['h3', 'h3'],
  ['h4', 'h4'],
  ['h5', 'h5'],
  ['h6', 'h6'],
  ['body-large', 'p'],
  ['body-medium', 'p'],
  ['body-small', 'p'],
  ['label-large', 'label'],
  ['label-medium', 'label'],
  ['label-small', 'label'],
  ['inherit', 'inherit'],
]);

const componentName = computed(() => props.as || variantTags.get(props.variant));
const textWrap = computed(() => (props.wrap ? `text-wrap-${props.wrap}` : null));
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.inherit {
  font: inherit;
}

.h1 {
  font: var(--t-typography-heading-h1-font);

  @include mobile {
    font: var(--t-typography-heading-h1-font-mobile);
  }
}

.h2 {
  font: var(--t-typography-heading-h2-font);

  @include mobile {
    font: var(--t-typography-heading-h2-font-mobile);
  }
}

.h3 {
  font: var(--t-typography-heading-h3-font);
}

.h4 {
  font: var(--t-typography-heading-h4-font);
}

.h5 {
  font: var(--t-typography-heading-h5-font);
}

.h6 {
  font: var(--t-typography-heading-h6-font);
}

.body-large {
  font: var(--t-typography-body-lg-font);
}

.body-medium {
  font: var(--t-typography-body-md-font);
}

.body-small {
  font: var(--t-typography-body-sm-font);
}

label {
  cursor: inherit;

  &[for] {
    cursor: pointer;
  }
}

.label-large {
  font: var(--t-typography-label-lg-font);
}

.label-medium {
  font: var(--t-typography-label-md-font);
}

.label-small {
  font: var(--t-typography-label-sm-font);
}

.color-primary {
  color: var(--t-text-color-primary);
}

.color-secondary {
  color: var(--t-text-color-secondary);
}

.color-tertiary {
  color: var(--t-text-color-tertiary);
}

.color-inherit {
  color: inherit;
}

.align-inherit {
  text-align: inherit;
}

.align-start {
  text-align: start;
}

.align-center {
  text-align: center;
}

.align-end {
  text-align: end;
}

.text-wrap-wrap {
  text-wrap: wrap;
}

.text-wrap-nowrap {
  text-wrap: nowrap;
}

.text-wrap-balance {
  text-wrap: balance;
}

.text-wrap-pretty {
  text-wrap: pretty;
}

.text-wrap-inherit {
  text-wrap: inherit;
}
</style>
