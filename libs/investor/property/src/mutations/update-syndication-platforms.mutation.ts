import { defineMutation, useMutation, useQueryCache } from '@pinia/colada';

import { useAlertsStore } from '@tallo/design-system';

import { SaveSyndicationDto } from '../dtos/save-syndication.dto.interface';
import { getPropertySyndicationPlatformsQuery<PERSON>ey } from '../queries/property-syndication-platforms.query';
import propertyListingService from '../services/property-listing.service';

export const useUpdateSyndicationPlatforms = defineMutation(() => {
  return useMutation({
    mutation(payload: { propertyId: string; syndicationDto: SaveSyndicationDto }) {
      return propertyListingService.updateSyndication(payload.propertyId, payload.syndicationDto);
    },

    async onSuccess(_, payload) {
      const queryCache = useQueryCache();
      const alertsStore = useAlertsStore();

      // Invalidate the syndication platforms query to refresh the data
      await queryCache.invalidateQueries({
        key: getPropertySyndicationPlatforms<PERSON><PERSON><PERSON><PERSON><PERSON>(payload.propertyId),
      });

      alertsStore.success('Syndication platforms updated successfully');
    },

    onError() {
      const alertsStore = useAlertsStore();
      alertsStore.error('Failed to update syndication platforms');
    },
  });
});
