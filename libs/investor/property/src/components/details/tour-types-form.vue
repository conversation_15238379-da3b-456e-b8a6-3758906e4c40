<template>
  <View direction="column" gap="xl">
    <Text variant="body-medium" color="secondary" align="start">
      Select the tour options you want to offer to potential tenants. At least one option must be selected.
    </Text>

    <View direction="column" gap="md">
      <CheckboxCard
        title="In-person tour"
        description="Showcasing your property to tenants personally or by a leasing agent"
        icon="user-01"
        v-model="form.fields.allowsInPersonTours"
      />
      <CheckboxCard
        title="Self-guided tour"
        description="Showcasing your property to tenants by providing them with instructions to access the property"
        icon="key"
        v-model="form.fields.allowsSelfGuidedTours"
      />
      <CheckboxCard
        title="Virtual tour"
        description="Showcasing your property to tenants online via apps like FaceTime or Zoom"
        icon="video-recorder"
        v-model="form.fields.allowsVirtualTours"
      />
    </View>
  </View>
</template>

<script setup lang="ts">
import { watch, computed } from 'vue';

import { Text, View, CheckboxCard, useForm } from '@tallo/design-system';

import { SaveTourTypesDto } from '../../dtos/save-tour-types.dto.interface';
import { usePropertyStore } from '../../stores/property.store';

const propertyStore = usePropertyStore();

const atLeastOneTourType = (value: boolean, siblings: SaveTourTypesDto) => {
  return value || siblings.allowsInPersonTours || siblings.allowsVirtualTours || siblings.allowsSelfGuidedTours;
};

const form = useForm<SaveTourTypesDto>(
  {
    allowsInPersonTours: true,
    allowsVirtualTours: true,
    allowsSelfGuidedTours: true,
  },
  {
    allowsInPersonTours: { atLeastOneTourType },
    allowsVirtualTours: { atLeastOneTourType },
    allowsSelfGuidedTours: { atLeastOneTourType },
  },
);

const isFormValid = computed(() => {
  return form.fields.allowsInPersonTours || form.fields.allowsVirtualTours || form.fields.allowsSelfGuidedTours;
});

function populateForm() {
  const property = propertyStore.property;
  Object.assign(form.fields, {
    // If value is null, default to false; otherwise use the existing value
    allowsInPersonTours: property?.allowsInPersonTours ?? false,
    allowsVirtualTours: property?.allowsVirtualTours ?? false,
    allowsSelfGuidedTours: property?.allowsSelfGuidedTours ?? false,
  });
}

async function save() {
  return form.submit(async (fields) => {
    await propertyStore.saveTourTypes(fields);
  });
}

watch(() => propertyStore.property, populateForm, { immediate: true });

defineExpose({ save, form, isFormValid });
</script>
