import { useQuery } from '@pinia/colada';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import propertyListingService from '../services/property-listing.service';

export function getPropertySyndicationPlatformsQueryKey(propertyId: string) {
  return ['property', propertyId, 'syndication-platforms'];
}

export function usePropertySyndicationPlatformsQuery() {
  const route = useRoute();
  const propertyId = computed(() => route.params.propertyId as string);

  const query = useQuery({
    key: () => getPropertySyndicationPlatformsQueryKey(propertyId.value),
    query: async () => {
      const response = await propertyListingService.getSyndicationPlatforms(propertyId.value);
      return response.data;
    },
  });

  return {
    ...query,
    platforms: computed(() => query.data.value?.platforms || []),
  };
}
