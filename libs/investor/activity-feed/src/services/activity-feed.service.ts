import axios from 'axios';

import { ActivityFeedItemDto, ActivityFeedQueryParams } from '../interfaces/activity-feed-item.dto';

class ActivityFeedService {
  async getList(params: ActivityFeedQueryParams = {}): Promise<ActivityFeedItemDto[]> {
    const response = await axios.get<ActivityFeedItemDto[]>(`activity-feed/owner`, {
      params,
    });

    return response.data;
  }
}

export default new ActivityFeedService();
