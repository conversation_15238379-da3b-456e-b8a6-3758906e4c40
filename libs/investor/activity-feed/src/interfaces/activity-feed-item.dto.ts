import { ActivityFeedEventName } from '../enums/activity-feed-event-name.enum';

export interface ActivityFeedItemDto {
  id: string;
  eventName: ActivityFeedEventName;
  createdAt: string;
  property: ActivityFeedItemPropertyDto | null;
  user: ActivityFeedItemUserDto | null;
}

export interface ActivityFeedItemPropertyDto {
  id: string;
  displayName: string;
}

export interface ActivityFeedItemUserDto {
  id: string;
  name: string;
}

export interface ActivityFeedQueryParams {
  limit?: number;
  after?: string;
}
