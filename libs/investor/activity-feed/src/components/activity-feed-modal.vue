<template>
  <Modal position="end" ref="modalDialogRef">
    <template #header>Activity feed</template>
    <template #body>
      <View v-if="isLoading.value" justify="center" padding="xl">
        <LoadingIndicator />
      </View>

      <ActivityFeedList v-else :items="data" />
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { LoadingIndicator, Modal, View } from '@tallo/design-system';

import { useActivityFeedQuery } from '../queries/activity-feed.query';
import ActivityFeedList from './activity-feed-list.vue';

const modalDialogRef = ref<InstanceType<typeof Modal>>();

const activityFeedQuery = useActivityFeedQuery();
const data = computed(() => activityFeedQuery.data?.value || []);
const isLoading = computed(() => activityFeedQuery.isLoading);

function open() {
  modalDialogRef.value?.show();
}

defineExpose({ open });
</script>

<style scoped lang="scss"></style>
