<template>
  <View gap="sm" direction="column">
    <ActivityFeedItemCard v-for="item in items" :key="item.id" :item="item" />
  </View>
</template>

<script setup lang="ts">
import { View } from '@tallo/design-system';

import { ActivityFeedItemDto } from '../interfaces/activity-feed-item.dto';
import ActivityFeedItemCard from './activity-feed-item-card.vue';

defineProps<{ items: ActivityFeedItemDto[] }>();
</script>

<style scoped lang="scss"></style>
