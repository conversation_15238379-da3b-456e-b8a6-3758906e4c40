<template>
  <View justify="center" align="center" class="activity-feed-item-icon" :class="icon?.color">
    <Icon :name="icon.iconName" size="small" />
  </View>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { Icon, View, IconName } from '@tallo/design-system';

import { ActivityFeedEventName } from '../enums/activity-feed-event-name.enum';

type IconColor = 'green' | 'blue' | 'orange' | 'red' | 'violet' | 'blue-dust' | 'grey';
interface IconMapItem {
  iconName: IconName;
  color: IconColor;
}

const props = defineProps<{ eventName: ActivityFeedEventName }>();
const iconMap = new Map<ActivityFeedEventName, IconMapItem>([
  [ActivityFeedEventName.APPLICATION_SENT, { iconName: 'file-sent-02', color: 'blue' }],
  [ActivityFeedEventName.APPLICATION_COMPLETED, { iconName: 'file-check-02', color: 'green' }],
  [ActivityFeedEventName.APPLICATION_REQUESTED, { iconName: 'file-question-02', color: 'orange' }],
  [ActivityFeedEventName.SHOWING_ATTENDANCE_CONFIRMED, { iconName: 'calendar-check-02', color: 'green' }],
  [ActivityFeedEventName.SHOWING_REQUESTED, { iconName: 'calendar-question', color: 'orange' }],
  [ActivityFeedEventName.SHOWING_CONFIRMED, { iconName: 'calendar-check-02', color: 'green' }],
  [ActivityFeedEventName.SHOWING_COMPLETED, { iconName: 'calendar-check-01', color: 'blue-dust' }],
  [ActivityFeedEventName.SHOWING_ATTENDANCE_DECLINED, { iconName: 'calendar-x-02', color: 'red' }],
  [ActivityFeedEventName.NEW_LEAD, { iconName: 'user-plus-01', color: 'violet' }],
  [ActivityFeedEventName.PROPERTY_ADDED, { iconName: 'home-plus', color: 'grey' }],
  [ActivityFeedEventName.PROPERTY_LISTED, { iconName: 'clipboard-check', color: 'blue' }],
  [ActivityFeedEventName.PROPERTY_RENTED_OUT, { iconName: 'home-check', color: 'green' }],
  [ActivityFeedEventName.LEAD_QUALIFIED, { iconName: 'user-check-01', color: 'green' }],
]);

const icon = computed(() => iconMap.get(props.eventName) as IconMapItem);
</script>

<style scoped lang="scss">
.activity-feed-item-icon {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;

  &.green {
    color: #2e946d;
    background-color: #d9f2e4;
  }

  &.blue {
    color: #3387c4;
    background-color: #e2ecf3;
  }

  &.blue-dust {
    color: #6a808f;
    background-color: #eaeef4;
  }

  &.orange {
    color: #f8863f;
    background-color: #feead6;
  }

  &.red {
    color: #d95e5a;
    background-color: #faebe9;
  }

  &.violet {
    color: #85569e;
    background-color: #e8ddf1;
  }

  &.grey {
    color: #8b8e8f;
    background-color: #f1f2f2;
  }
}
</style>
