<template>
  <Card size="medium" variant="tertiary" :shadow="true" class="activity-feed-item-card">
    <View gap="sm">
      <ActivityFeedItemIcon :event-name="item.eventName" />

      <View :grow="1" direction="column">
        <Text variant="label-medium">{{ item.eventName }}</Text>
        <Text variant="body-medium" color="secondary">
          {{ item.property?.displayName }} &middot; {{ item.user?.name }}
        </Text>
      </View>

      <Text variant="body-small" color="tertiary" align="end" wrap="nowrap">
        <div>{{ date }},</div>
        <div>{{ time }}</div>
      </Text>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import { computed } from 'vue';

import { Card, Text, View } from '@tallo/design-system';

import { ActivityFeedItemDto } from '../interfaces/activity-feed-item.dto';
import ActivityFeedItemIcon from './activity-feed-item-icon.vue';

const props = defineProps<{ item: ActivityFeedItemDto }>();
const time = computed(() => useDateFormat(props.item.createdAt, 'hh:mm a').value);
const date = computed(() => {
  const date = new Date(props.item.createdAt);
  const now = new Date();

  const isSameDay = (a: Date, b: Date) => {
    return a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth() && a.getDate() === b.getDate();
  };

  const isYesterday = (d: Date) => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return isSameDay(d, yesterday);
  };

  if (isSameDay(date, now)) {
    return 'Today';
  }

  if (isYesterday(date)) {
    return 'Yesterday';
  }

  return useDateFormat(props.item.createdAt, 'MMM D').value;
});
</script>

<style scoped lang="scss">
.activity-feed-item-card {
  flex-grow: 1;
  box-shadow: 0 1px 6px 0 rgba(43, 43, 44, 0.06);
}
</style>
