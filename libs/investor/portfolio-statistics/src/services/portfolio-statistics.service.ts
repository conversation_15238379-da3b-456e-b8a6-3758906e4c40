import axios from 'axios';

import { PortfolioStatisticsFilter } from '../enums/portfolio-statistics-filter.enum';
import { PortfolioStatisticsDto } from '../interfaces/portfolio-statistics.dto';
import { PortfolioTimeSeriesDto } from '../interfaces/portfolio-time-series.dto';

class PortfolioStatisticsService {
  async get(filter: PortfolioStatisticsFilter): Promise<PortfolioStatisticsDto> {
    const response = await axios.get<PortfolioStatisticsDto>('portfolio-statistics/owner', {
      params: {
        filter,
      },
    });

    return response.data;
  }

  async getTimeSeries(filter: PortfolioStatisticsFilter): Promise<PortfolioTimeSeriesDto> {
    const response = await axios.get<PortfolioTimeSeriesDto>('portfolio-statistics/owner/time-series', {
      params: {
        filter,
      },
    });

    return response.data;
  }
}

export default new PortfolioStatisticsService();
