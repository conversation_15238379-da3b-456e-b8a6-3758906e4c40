import { useQuery } from '@pinia/colada';
import { Ref } from 'vue';

import { PortfolioStatisticsFilter } from '../enums/portfolio-statistics-filter.enum';
import portfolioStatisticsService from '../services/portfolio-statistics.service';

export function getPortfolioTimeSeriesQueryKey(filter: PortfolioStatisticsFilter) {
  return ['portfolio-time-series', filter];
}

export function usePortfolioTimeSeriesQuery(filter: Ref<PortfolioStatisticsFilter>) {
  return useQuery({
    key: () => getPortfolioTimeSeriesQueryKey(filter.value),
    query: () => portfolioStatisticsService.getTimeSeries(filter.value),
  });
}
