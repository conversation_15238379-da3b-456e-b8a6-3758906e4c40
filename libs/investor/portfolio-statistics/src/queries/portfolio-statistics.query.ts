import { useQuery } from '@pinia/colada';
import { Ref } from 'vue';

import { PortfolioStatisticsFilter } from '../enums/portfolio-statistics-filter.enum';
import portfolioStatisticsService from '../services/portfolio-statistics.service';

export function getPortfolioStatisticsQueryKey(filter: PortfolioStatisticsFilter) {
  return ['portfolio-statistics', filter];
}

export function usePortfolioStatisticsQuery(filter: Ref<PortfolioStatisticsFilter>) {
  return useQuery({
    key: () => getPortfolioStatisticsQueryKey(filter.value),
    query: () => portfolioStatisticsService.get(filter.value),
  });
}
