import { PortfolioStatisticsDto } from './portfolio-statistics.dto';

export enum PortfolioTimeSeriesGranularity {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
}

export interface PortfolioTimeSeriesDto {
  dataPoints: TimeSeriesDataPoint[];
  granularity: PortfolioTimeSeriesGranularity;
  startDate: string;
  endDate: string;
}

export interface TimeSeriesDataPoint extends PortfolioStatisticsDto {
  timestamp: string;
}
