import { computed } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';

import { useApplicationBundlesQuery } from '../queries/application-bundles.query';

export function useApplicationBundles() {
  const { data, ...rest } = useApplicationBundlesQuery();
  const applicationBundles = computed(() => data?.value || []);
  const readeyForReview = computed(() => applicationBundles.value);

  return {
    ...rest,
    data,
    applicationBundles,
    readyForReview: readeyForReview,
  };
}
