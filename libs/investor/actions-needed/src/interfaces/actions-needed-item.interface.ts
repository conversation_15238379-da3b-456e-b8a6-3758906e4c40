import { ApplicationBundleDto } from '@tallo/investor/applications';
import { IntelligentEscalationDto } from '@tallo/investor/intelligent-escalations';
import { ShowingDto } from '@tallo/investor/showing';

export enum ActionNeededType {
  IntelligentEscalation = 'IntelligentEscalation',
  ShowingRequest = 'ShowingRequest',
  Application = 'Application',
}

export interface IntelligentEscalationItem {
  type: ActionNeededType.IntelligentEscalation;
  item: IntelligentEscalationDto;
}

export interface ShowingItem {
  type: ActionNeededType.ShowingRequest;
  item: ShowingDto;
}

export interface ApplicationItem {
  type: ActionNeededType.Application;
  item: ApplicationBundleDto;
}

export type ActionNeeded = IntelligentEscalationItem | ShowingItem | ApplicationItem;
