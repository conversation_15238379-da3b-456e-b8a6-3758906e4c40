import { computed, ref } from 'vue';

import { useApplicationBundles } from '@tallo/investor/applications';
import { useIntelligentEscalationsQuery } from '@tallo/investor/intelligent-escalations';
import { useShowings } from '@tallo/investor/showing';

import {
  ActionNeededType,
  ApplicationItem,
  IntelligentEscalationItem,
  ShowingItem,
} from '../interfaces/actions-needed-item.interface';

export function useActionsNeeded() {
  const intelligentEscalationsQuery = useIntelligentEscalationsQuery();
  const intelligentEscalations = computed(() => intelligentEscalationsQuery.data?.value || []);

  const { pendingShowings, isLoading: isLoadingShowings } = useShowings();
  const applicationBundles = useApplicationBundles();
  const isLoading = computed(
    () => intelligentEscalationsQuery.isLoading.value || applicationBundles.isLoading.value || isLoadingShowings.value,
  );

  const actionsNeeded = computed(() => {
    const intelligentEscalationItems: IntelligentEscalationItem[] = intelligentEscalations.value.map((item) => ({
      type: ActionNeededType.IntelligentEscalation,
      item,
    }));

    const pendingShowingItems: ShowingItem[] = pendingShowings.value.map((item) => ({
      type: ActionNeededType.ShowingRequest,
      item,
    }));

    const applicationItems: ApplicationItem[] = applicationBundles.readyForReview.value.map((item) => ({
      type: ActionNeededType.Application,
      item,
    }));

    let data = [...intelligentEscalationItems, ...pendingShowingItems, ...applicationItems];

    return data.sort((a, b) => new Date(b.item.createdAt).getTime() - new Date(a.item.createdAt).getTime());
  });

  return {
    data: actionsNeeded,
    isLoading,
  };
}
