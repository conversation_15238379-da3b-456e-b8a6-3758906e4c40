import { useQuery } from '@pinia/colada';
import { computed, ref } from 'vue';

import { propertyInquiryService } from '@tallo/investor/property';

export const renterConversationQueryKey = 'renter-conversation';

export const getRenterConversationQueryKey = (inquiryId: string) => {
  return [renterConversationQueryKey, inquiryId];
};

export function useRenterConversationQuery() {
  const activeInquiryId = ref<string | null>(null);

  const query = useQuery({
    key: () => getRenterConversationQueryKey(activeInquiryId.value!),
    enabled: () => Boolean(activeInquiryId.value),
    query: () => {
      return activeInquiryId.value
        ? propertyInquiryService.getInquiryConversation(activeInquiryId.value!)
        : Promise.resolve(null);
    },
  });

  const setActiveInquiry = (inquiryId: string | null) => {
    activeInquiryId.value = inquiryId;
  };

  return {
    ...query,
    setActiveInquiry,
    activeInquiryId: computed(() => activeInquiryId.value),
  };
}
