import { useQuery } from '@pinia/colada';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { propertyInquiryService } from '@tallo/investor/property';

export const renterInquiriesQueryKey = 'renter-inquiries';

export const getRenterInquiriesQueryKey = (renterId: string) => {
  return [renterInquiriesQueryKey, renterId];
};

export function useRenterInquiriesQuery() {
  const route = useRoute();
  const renterId = computed(() => route.params.renterId as string);

  return useQuery({
    key: () => getRenterInquiriesQueryKey(renterId.value),
    enabled: () => Boolean(renterId.value),
    query: () => propertyInquiryService.getAllInquiriesByRenter(renterId.value),
  });
}
