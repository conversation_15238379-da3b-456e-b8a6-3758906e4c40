import axios from 'axios';

import { InvestorOnboardingSignUpDto } from '../dtos/investor-onboarding-sign-up.dto';

class InvestorOnboardingService {
  async fillCompanySignUpInfo(companyId: string, payload: InvestorOnboardingSignUpDto): Promise<void> {
    await axios.post(`/investor-onboarding/sign-up-info/${companyId}`, payload);
  }

  // New completion endpoints
  async completePropertyOnboarding(): Promise<void> {
    await axios.post(`/investor-onboarding/complete/property`);
  }

  async completeDashboardOnboarding(): Promise<void> {
    await axios.post(`/investor-onboarding/complete/dashboard`);
  }

  async completeRenterProfileOnboarding(): Promise<void> {
    await axios.post(`/investor-onboarding/complete/renter-profile`);
  }
}

export default new InvestorOnboardingService();
