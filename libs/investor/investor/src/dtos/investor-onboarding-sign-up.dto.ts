import { CompanyNumberOfEmployees } from '@tallo/investor/company';
import { ScreeningSensitivity } from '@tallo/property';

export type CompanyType = 'Company' | 'Individual';

export interface InvestorOnboardingSignUpDto {
  phoneNumber?: string;
  companyName?: string;
  numberOfEmployees?: CompanyNumberOfEmployees;
  numberOfUnitsOverseeing?: number;
  perceivedProductValueProposition?: string;
  preferredCommunicationChannel?: string;
  accountType?: CompanyType;
  screeningSensitivity?: ScreeningSensitivity | null;
}
