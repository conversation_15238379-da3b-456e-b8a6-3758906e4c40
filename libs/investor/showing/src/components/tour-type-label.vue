<template>
  <View
    v-if="shouldShowLabel && tourTypeConfig"
    direction="row"
    gap="sm"
    align="center"
    :class="['tour-type-label', tourTypeConfig.className]"
  >
    <Icon :name="tourTypeConfig.icon" size="extra-small" />
    <Text wrap="nowrap" variant="label-small">{{ tourTypeConfig.label }}</Text>
  </View>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { Text, View, Icon, type IconName } from '@tallo/design-system';

import { TourType } from '../interfaces/tour-type.enum';

interface TourTypeLabelProps {
  tourType: TourType;
}

interface TourTypeConfig {
  label: string;
  icon: IconName;
  className: string;
}

const props = defineProps<TourTypeLabelProps>();

const tourTypeConfigs: Partial<Record<TourType, TourTypeConfig>> = {
  [TourType.VIRTUAL]: {
    label: 'Virtual tour',
    icon: 'video-recorder',
    className: 'tour-type-virtual',
  },
};

const shouldShowLabel = computed(() => {
  // Only show label for tour types that have configuration (excludes IN_PERSON)
  return props.tourType in tourTypeConfigs;
});

const tourTypeConfig = computed(() => {
  return tourTypeConfigs[props.tourType as keyof typeof tourTypeConfigs];
});
</script>

<style scoped lang="scss">
.tour-type-label {
  width: fit-content;
  padding: 0.25rem 0.5rem;
  border-radius: var(--t-border-radius-xs);
  max-height: fit-content;
}

.tour-type-virtual {
  background-color: var(--t-color-orange-200);
}
</style>
