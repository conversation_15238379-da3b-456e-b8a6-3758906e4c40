<template>
  <Card variant="tertiary" :size="size" class="user-card">
    <View :gap="size === 'medium' ? 'md' : 'sm'">
      <Avatar :class="{ 'medium-size-avatar': size === 'medium' }" :user-name="user.name!" />
      <View direction="column" justify="center">
        <Text :variant="labelFontsVariant">{{ timeOnly }} · {{ user.name }}</Text>
        <div class="contact-info">
          <Text :variant="bodyFontsVariant" as="span" color="secondary">
            {{ address }}
          </Text>
        </div>
      </View>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { Card, Text, Avatar, View } from '@tallo/design-system';
import { UserDto } from '@tallo/user';

import { ShowingDto } from '../interfaces/showing.interface';
import { getShowingTimeOnly } from '../utils/get-showing-time-only.util';

const props = withDefaults(
  defineProps<{
    user: Omit<UserDto, 'id'>;
    showing: ShowingDto;
    address: string;
    size: 'medium' | 'small';
  }>(),
  {
    size: 'medium',
  },
);

const labelFontsVariant = computed(() => (props.size === 'medium' ? 'label-medium' : 'label-small'));
const bodyFontsVariant = computed(() => (props.size === 'medium' ? 'body-medium' : 'body-small'));
const timeOnly = computed(() => getShowingTimeOnly(props.showing.startTime, props.showing.endTime));
</script>

<style scoped lang="scss">
.user-card {
  width: 100%;
}

.medium-size-avatar {
  $avatar-size: 3rem;
  width: $avatar-size;
  height: $avatar-size;
  font: var(--t-typography-label-lg-font);
}
</style>
