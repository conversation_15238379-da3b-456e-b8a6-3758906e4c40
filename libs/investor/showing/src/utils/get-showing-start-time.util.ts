import { parse } from 'date-fns';

/**
 * Calculates the start time for a showing based on the given time and date
 * @param time - Time string in 'h:mm a' format (e.g., '8:00 am')
 * @param date - Base date to combine with the time
 * @returns startTime as Date object
 */
export function getShowingStartTime(time: string, date: Date): Date {
  const startTime = parse(time, 'h:mm a', date);

  return startTime;
}
