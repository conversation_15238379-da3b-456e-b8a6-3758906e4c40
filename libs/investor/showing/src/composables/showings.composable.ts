import { computed, MaybeRef, unref } from 'vue';

import { ShowingStatus } from '../interfaces/showing-status.enum';
import { useShowingsQuery } from '../queries/showings.query';
import { groupShowingByDateAndSort } from '../utils/group-showing-by-date.util';
import { getPendingShowings } from '../utils/pending-showings.util';

export function useShowings(propertyIdRef?: MaybeRef<string>) {
  const { data, ...rest } = useShowingsQuery();

  const showings = computed(() => {
    const allShowings = data.value || [];
    const propertyId = propertyIdRef ? unref(propertyIdRef) : null;

    return propertyId ? allShowings.filter(({ property }) => propertyId === property.id) : allShowings;
  });
  const pendingShowings = computed(() => getPendingShowings(showings.value));
  const upcomingShowings = computed(() => showings.value.filter(({ status }) => status === ShowingStatus.CONFIRMED));
  const groupedByDateUpcomingShowings = computed(() => groupShowingByDateAndSort(upcomingShowings.value));

  return {
    ...rest,
    data,
    showings,
    pendingShowings,
    groupedByDateUpcomingShowings,
  };
}
