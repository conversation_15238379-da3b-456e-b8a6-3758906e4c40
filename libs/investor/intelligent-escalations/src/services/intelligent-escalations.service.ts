import axios from 'axios';

import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';

class IntelligentEscalationService {
  async getByCompany(companyId: string): Promise<IntelligentEscalationDto[]> {
    const response = await axios.get(`intelligent-escalation/by-company/${companyId}`);

    return response.data;
  }

  async getByProperty(propertyId: string): Promise<IntelligentEscalationDto[]> {
    const response = await axios.get(`intelligent-escalation/${propertyId}`);

    return response.data;
  }

  async answer(escalationId: string, answer: string): Promise<IntelligentEscalationDto> {
    const response = await axios.post(`intelligent-escalation/answer/${escalationId}`, {
      answer,
    });

    return response.data;
  }

  async ignore(escalationId: string): Promise<IntelligentEscalationDto> {
    const response = await axios.put(`intelligent-escalation/ignore/${escalationId}`);

    return response.data;
  }

  async shareSelfShowingInstructions(
    escalationId: string,
    instructions: { instructions: string; codeValidityPeriod: string },
  ): Promise<IntelligentEscalationDto> {
    const response = await axios.post(`intelligent-escalation/self-showing-instructions/share/${escalationId}`, {
      instructions,
    });

    return response.data;
  }
}

export default new IntelligentEscalationService();
