<template>
  <Modal position="end" ref="modalDialogRef" @after-close="emit('closed')">
    <template #header>Self-Showing Instructions Request</template>

    <template #body>
      <View gap="lg" direction="column">
        <View direction="column" gap="lg">
          <Text variant="h3">Share self-showing instructions</Text>
          <Text variant="body-medium" color="tertiary">
            Please share the instruction on how to access the property for the self-guided tour. Once shared <PERSON><PERSON> will
            pass the instructions to the renter `
          </Text>
        </View>
        <View class="full-width">
          <Card variant="secondary" class="full-width">
            <template #header>
              <View class="full-width" direction="row" gap="sm" justify="space-between" align="center">
                <Text variant="h5">Showing info</Text>
                <Badge variant="violet" label="Self-guided" size="small" />
              </View>
            </template>
            <View direction="column" gap="md">
              <RenterShowingCard
                v-if="renter && selectedShowing"
                :user="renter.user"
                :showing="selectedShowing!"
                :address="escalation.property.displayName"
                size="small"
              />

              <View direction="column" gap="sm">
                <Text variant="label-medium">Add instructions here</Text>
                <Textarea
                  placeholder="Type self-showing access instructions here"
                  rows="8"
                  v-model="form.fields.instructions"
                />
              </View>

              <FormField label="Choose how long the shared code is valid">
                <SegmentedSelect v-model="form.fields.codeValidityPeriod" :options="tokenDurationOptions" />
              </FormField>
            </View>
          </Card>
        </View>
      </View>
    </template>
    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <View gap="md">
        <Button
          :disabled="form.validation.$invalid || isSharing"
          :loading="form.submitting || isSharing"
          @click="submit"
        >
          Send
        </Button>
      </View>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { required } from '@vuelidate/validators';
import { computed, ref } from 'vue';

import { Badge, Button, Card, Modal, Text, View, Textarea, FormField, useForm } from '@tallo/design-system';
import { RenterShowingCard, useShowings } from '@tallo/investor/showing';
import { RenterDto } from '@tallo/renter';

import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';
import { useShareSelfShowingInstructions } from '../mutations/share-self-showing-instructions.mutation';
import SegmentedSelect from './segmented-select.vue';

const emit = defineEmits(['closed']);

const props = defineProps<{
  escalation: IntelligentEscalationDto;
  renter?: RenterDto;
}>();

const modalDialogRef = ref<InstanceType<typeof Modal> | null>(null);

// Get showings for this property and find a showing that involves this renter
const { showings } = useShowings(computed(() => props.escalation.property.id));
const selectedShowing = computed(() => {
  const renterId = props.renter?.id;
  if (!renterId) return undefined;

  const candidates = (showings.value || []).filter((s) => s.showingRequests.some((r) => r.renter.id === renterId));
  if (!candidates.length) return undefined;

  const now = Date.now();
  const upcoming = candidates
    .filter((s) => new Date(s.startTime).getTime() >= now)
    .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
  if (upcoming.length) return upcoming[0];

  return candidates.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())[0];
});

const tokenDurationOptions = [
  { label: '1 hour', value: '1h' },
  { label: '2 hours', value: '2h' },
  { label: '3 hours', value: '3h' },
  { label: '1 day', value: '24h' },
  { label: 'Unlimited', value: 'unlimited' },
];

const shareSelfShowingInstructions = useShareSelfShowingInstructions();
const isSharing = computed(() => shareSelfShowingInstructions.isLoading.value);

const form = useForm<{ instructions: string; codeValidityPeriod: string }>(
  { instructions: '', codeValidityPeriod: tokenDurationOptions[0].value },
  { instructions: { required }, codeValidityPeriod: { required } },
);

async function submit() {
  await form.submit(async () => {
    await shareSelfShowingInstructions.mutateAsync({
      propertyId: props.escalation.property.id,
      escalationId: props.escalation.id,
      instructions: {
        instructions: form.fields.instructions,
        codeValidityPeriod: form.fields.codeValidityPeriod,
      },
    });

    close();
  });
}

function open() {
  modalDialogRef.value?.show();
}

function close() {
  modalDialogRef.value?.close();
}

defineExpose({
  open,
  close,
  modalDialogRef,
});
</script>

<style scoped lang="scss">
.full-width {
  width: 100%;
}
</style>
