<template>
  <Modal position="end" ref="modalDialogRef" @after-close="emit('closed')">
    <template #header>Phone Call Request</template>
    <template #body>
      <View direction="column" gap="lg">
        <View direction="column" gap="sm">
          <Text variant="h3"><PERSON><PERSON> requested a phone call</Text>
          <Text variant="body-medium" color="tertiary">
            A renter has requested to speak with you directly. Please reach out to them at your earliest convenience.
          </Text>
        </View>

        <RouterLink
          v-if="firstRenter"
          :to="renterProfileRoute(escalation.property.id, firstRenter.id)"
          class="user-card-link"
        >
          <UserCard :user="firstRenter.user" size="medium" />
        </RouterLink>

        <View direction="column" gap="sm">
          <Text variant="h5">Context</Text>
          <Text variant="body-medium">{{ escalation.questionText }}</Text>
        </View>
      </View>
    </template>

    <template #footer>
      <Button variant="ghost" @click="close">Close</Button>
      <Button :loading="isResolvingEscalation" @click="resolve">Mark as Resolved</Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { RouterLink } from 'vue-router';

import { Button, Modal, View, Text } from '@tallo/design-system';
import { renterProfileRoute } from '@tallo/investor/navigation';
import { UserCard } from '@tallo/user';

import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';
import { useIgnoreIntelligentEscalation } from '../mutations/ignore-intelligent-escalation.mutation';

const emit = defineEmits(['closed']);
const props = defineProps<{ escalation: IntelligentEscalationDto }>();

const ignoreIntelligentEscalation = useIgnoreIntelligentEscalation();
const isResolvingEscalation = computed(() => ignoreIntelligentEscalation.isLoading.value);
const modalDialogRef = ref<InstanceType<typeof Modal> | null>(null);

const firstRenter = computed(() => props.escalation.renters?.[0]);

function open() {
  modalDialogRef.value?.show();
}

function close() {
  modalDialogRef.value?.close();
}

async function resolve() {
  await ignoreIntelligentEscalation.mutateAsync({
    propertyId: props.escalation.property.id,
    escalationId: props.escalation.id,
  });

  close();
}

defineExpose({
  open,
  close,
  modalDialogRef,
});
</script>

<style scoped lang="scss">
.user-card-link {
  text-decoration: none;
  cursor: pointer;
}
</style>
