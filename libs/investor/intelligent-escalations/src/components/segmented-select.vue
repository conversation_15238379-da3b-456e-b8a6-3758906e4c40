<template>
  <View direction="row" gap="sm" class="segmented-select" :class="{ wrap }">
    <Button
      v-for="opt in options"
      :key="opt.value"
      :variant="opt.value === modelValue ? 'primary' : 'outline'"
      size="small"
      @click="onSelect(opt.value)"
    >
      {{ opt.label }}
    </Button>
  </View>
</template>

<script setup lang="ts">
import { Button, View } from '@tallo/design-system';

interface OptionItem {
  label: string;
  value: string | number;
}

withDefaults(
  defineProps<{
    options: OptionItem[];
    modelValue: string | number | null;
    wrap?: boolean;
  }>(),
  { wrap: false },
);

const emit = defineEmits(['update:modelValue']);

function onSelect(value: string | number) {
  emit('update:modelValue', value);
}
</script>

<style scoped lang="scss">
.segmented-select {
  flex-wrap: nowrap;
  &.wrap {
    flex-wrap: wrap;
  }
}
</style>
