<template>
  <Card variant="secondary" size="medium" class="property-question-card">
    <template #header>
      <Text variant="h5">{{ question.title }}</Text>
    </template>

    <View direction="column" gap="md">
      <Text variant="body-medium" color="primary" class="question-text">
        {{ question.questionText }}
      </Text>

      <View align="center" justify="space-between">
        <AvatarsRowList :renters="question.renters" :property-id="question.property.id" />
        <Text variant="body-small" color="tertiary">
          {{ useDateFormat(question.createdAt, 'MMM D') }}
        </Text>
      </View>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';

import { Card, Text, View } from '@tallo/design-system';
import { AvatarsRowList } from '@tallo/investor/renter';

import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';

defineProps<{ question: IntelligentEscalationDto }>();
</script>

<style scoped lang="scss">
.property-question-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.question-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
