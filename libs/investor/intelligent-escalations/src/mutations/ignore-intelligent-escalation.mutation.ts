import { defineMutation, useMutation, useQueryCache } from '@pinia/colada';

import { useAlertsStore } from '@tallo/design-system';
import { portfolioQueryKey, getPropertyStatisticsQueryKey } from '@tallo/investor/property';

import { getPropertyIntelligentEscalationsQueryKey } from '../queries/property-intelligent-escalations.query';
import intelligentEscalationService from '../services/intelligent-escalations.service';

export const useIgnoreIntelligentEscalation = defineMutation(() => {
  return useMutation({
    mutation(payload: { propertyId: string; escalationId: string }) {
      return intelligentEscalationService.ignore(payload.escalationId);
    },

    async onSuccess(_, payload) {
      const queryCache = useQueryCache();
      const alertsStore = useAlertsStore();

      queryCache.invalidateQueries({ key: getPropertyStatisticsQueryKey(payload.propertyId) });
      queryCache.invalidateQueries({ key: [portfolioQueryKey] });
      await queryCache.invalidateQueries({ key: getPropertyIntelligentEscalationsQueryKey(payload.propertyId) });

      alertsStore.success('Escalation ignored');
    },

    onError() {
      const alertsStore = useAlertsStore();

      alertsStore.error('Error ignoring escalation');
    },
  });
});
