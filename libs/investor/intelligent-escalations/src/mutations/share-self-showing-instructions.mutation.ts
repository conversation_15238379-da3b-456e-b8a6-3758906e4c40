import { defineMutation, useMutation, useQueryCache } from '@pinia/colada';

import { useAlertsStore } from '@tallo/design-system';
import { portfolioQueryKey, getPropertyStatisticsQueryKey } from '@tallo/investor/property';

import { getPropertyIntelligentEscalationsQueryKey } from '../queries/property-intelligent-escalations.query';
import intelligentEscalationService from '../services/intelligent-escalations.service';

export const useShareSelfShowingInstructions = defineMutation(() => {
  return useMutation({
    mutation(payload: {
      propertyId: string;
      escalationId: string;
      instructions: { instructions: string; codeValidityPeriod: string };
    }) {
      return intelligentEscalationService.shareSelfShowingInstructions(payload.escalationId, payload.instructions);
    },

    async onSuccess(_, payload) {
      const queryCache = useQueryCache();
      const alertsStore = useAlertsStore();

      queryCache.invalidateQueries({ key: getPropertyStatisticsQueryKey(payload.propertyId) });
      queryCache.invalidateQueries({ key: [portfolioQueryKey] });
      await queryCache.invalidateQueries({ key: getPropertyIntelligentEscalationsQueryKey(payload.propertyId) });

      alertsStore.success('Instructions shared');
    },

    onError() {
      const alertsStore = useAlertsStore();

      alertsStore.error('Error sharing instructions');
    },
  });
});
