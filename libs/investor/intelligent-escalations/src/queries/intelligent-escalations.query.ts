import { useQuery } from '@pinia/colada';

import { useCompanyQuery } from '@tallo/investor/company';

import intelligentEscalationService from '../services/intelligent-escalations.service';

export function getIntelligentEscalationsQueryKey() {
  return ['intelligent-escalations'];
}

export function useIntelligentEscalationsQuery() {
  return useQuery({
    key: () => getIntelligentEscalationsQueryKey(),
    query: async () => {
      const companyQuery = useCompanyQuery();
      const { data } = await companyQuery.refresh(true);

      return intelligentEscalationService.getByCompany(data!.id);
    },
  });
}
