import { useQuery } from '@pinia/colada';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import intelligentEscalationService from '../services/intelligent-escalations.service';

export function getPropertyIntelligentEscalationsQueryKey(propertyId: string) {
  return ['property', propertyId, 'intelligent-escalations'];
}

export function usePropertyIntelligentEscalationsQuery() {
  const route = useRoute();
  const propertyId = computed(() => route.params.propertyId as string);

  return useQuery({
    key: () => getPropertyIntelligentEscalationsQueryKey(propertyId.value),
    query: () => intelligentEscalationService.getByProperty(propertyId.value),
  });
}
