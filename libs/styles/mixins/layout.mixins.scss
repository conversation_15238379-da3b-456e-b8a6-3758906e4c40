@import '../shared/variables';

@mixin desktop {
  @media screen and (min-width: $screen-xl) {
    @content;
  }
}

@mixin tablet-start {
  @media screen and (min-width: calc(#{$screen-sm} + 0.063rem)) {
    @content;
  }
}

@mixin tablet {
  @media screen and (max-width: $screen-lg) {
    @content;
  }
}

@mixin mobile {
  @media screen and (max-width: $screen-sm) {
    @content;
  }
}

@mixin screen-min-sm {
  @media screen and (min-width: $screen-sm) {
    @content;
  }
}

@mixin screen-min-md {
  @media screen and (min-width: $screen-md) {
    @content;
  }
}

@mixin screen-min-lg {
  @media screen and (min-width: $screen-lg) {
    @content;
  }
}

@mixin screen-min-xl {
  @media screen and (min-width: $screen-xl) {
    @content;
  }
}

@mixin screen-min-2xl {
  @media screen and (min-width: $screen-2xl) {
    @content;
  }
}

@mixin screen-min-3xl {
  @media screen and (min-width: $screen-3xl) {
    @content;
  }
}

@mixin screen-max-xs {
  @media screen and (max-width: $screen-xs) {
    @content;
  }
}

@mixin screen-max-sm {
  @media screen and (max-width: $screen-sm) {
    @content;
  }
}

@mixin screen-max-md {
  @media screen and (max-width: $screen-md) {
    @content;
  }
}

@mixin screen-max-lg {
  @media screen and (max-width: $screen-lg) {
    @content;
  }
}

@mixin screen-max-xl {
  @media screen and (max-width: $screen-xl) {
    @content;
  }
}

@mixin screen-max-2xl {
  @media screen and (max-width: $screen-2xl) {
    @content;
  }
}

@mixin screen-max-3xl {
  @media screen and (max-width: $screen-3xl) {
    @content;
  }
}
