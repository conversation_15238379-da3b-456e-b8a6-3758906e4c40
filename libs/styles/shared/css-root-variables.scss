// do not import this file!
// use CSS variables instead, e.g. `color: var(--t-color-neutral-500);`

// this file should be imported only once in the root
// if you import it anywhere else it will populate CSS vars multiple times

:root {
  /* Typography */
  --t-font-family-primary: Inter, sans-serif;

  --t-font-size-300: 0.75rem;
  --t-font-size-350: 0.875rem;
  --t-font-size-400: 1rem;
  --t-font-size-500: 1.25rem;
  --t-font-size-600: 1.5rem;
  --t-font-size-700: 1.75rem;

  --t-line-height-400: 1rem;
  --t-line-height-500: 1.25rem;
  --t-line-height-600: 1.5rem;
  --t-line-height-700: 1.75rem;
  --t-line-height-800: 2rem;
  --t-line-height-900: 2.25rem;

  --t-font-weight-regular: 400;
  --t-font-weight-medium: 500;
  --t-font-weight-semi-bold: 600;

  /*
  * Typography - Heading
  */
  --t-typography-heading-font-weight: var(--t-font-weight-semi-bold);

  --t-typography-heading-h1-font-size: var(--t-font-size-700);
  --t-typography-heading-h1-line-height: var(--t-line-height-900);
  --t-typography-heading-h1-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h1-font-size) / var(--t-typography-heading-h1-line-height) var(--t-font-family-primary);

  --t-typography-heading-h1-font-size-mobile: var(--t-font-size-600);
  --t-typography-heading-h1-line-height-mobile: var(--t-line-height-800);
  --t-typography-heading-h1-font-mobile: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h1-font-size-mobile) / var(--t-typography-heading-h1-line-height-mobile)
    var(--t-font-family-primary);

  --t-typography-heading-h2-font-size: var(--t-font-size-600);
  --t-typography-heading-h2-line-height: var(--t-line-height-800);
  --t-typography-heading-h2-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h2-font-size) / var(--t-typography-heading-h2-line-height) var(--t-font-family-primary);

  --t-typography-heading-h2-font-size-mobile: var(--t-font-size-500);
  --t-typography-heading-h2-line-height-mobile: var(--t-line-height-700);
  --t-typography-heading-h2-font-mobile: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h2-font-size-mobile) / var(--t-typography-heading-h2-line-height-mobile)
    var(--t-font-family-primary);

  --t-typography-heading-h3-font-size: var(--t-font-size-500);
  --t-typography-heading-h3-line-height: var(--t-line-height-700);
  --t-typography-heading-h3-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h3-font-size) / var(--t-typography-heading-h3-line-height) var(--t-font-family-primary);

  --t-typography-heading-h4-font-size: var(--t-font-size-400);
  --t-typography-heading-h4-line-height: var(--t-line-height-600);
  --t-typography-heading-h4-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h4-font-size) / var(--t-typography-heading-h4-line-height) var(--t-font-family-primary);

  --t-typography-heading-h5-font-size: var(--t-font-size-350);
  --t-typography-heading-h5-line-height: var(--t-line-height-500);
  --t-typography-heading-h5-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h5-font-size) / var(--t-typography-heading-h5-line-height) var(--t-font-family-primary);

  --t-typography-heading-h6-font-size: var(--t-font-size-300);
  --t-typography-heading-h6-line-height: var(--t-line-height-400);
  --t-typography-heading-h6-font: normal var(--t-typography-heading-font-weight)
    var(--t-typography-heading-h6-font-size) / var(--t-typography-heading-h6-line-height) var(--t-font-family-primary);

  /*
  * Typography - Body
  */
  --t-typography-body-font-weight: var(--t-font-weight-regular);

  --t-typography-body-lg-font-size: var(--t-font-size-400);
  --t-typography-body-lg-line-height: var(--t-line-height-600);
  --t-typography-body-lg-font: normal var(--t-typography-body-font-weight) var(--t-typography-body-lg-font-size) /
    var(--t-typography-body-lg-line-height) var(--t-font-family-primary);

  --t-typography-body-md-font-size: var(--t-font-size-350);
  --t-typography-body-md-line-height: var(--t-line-height-500);
  --t-typography-body-md-font: normal var(--t-typography-body-font-weight) var(--t-typography-body-md-font-size) /
    var(--t-typography-body-md-line-height) var(--t-font-family-primary);

  --t-typography-body-sm-font-size: var(--t-font-size-300);
  --t-typography-body-sm-line-height: var(--t-line-height-400);
  --t-typography-body-sm-font: normal var(--t-typography-body-font-weight) var(--t-typography-body-sm-font-size) /
    var(--t-typography-body-sm-line-height) var(--t-font-family-primary);

  /*
  * Typography - Label
  */
  --t-typography-label-font-weight: var(--t-font-weight-medium);

  --t-typography-label-lg-font-size: var(--t-font-size-400);
  --t-typography-label-lg-line-height: var(--t-line-height-600);
  --t-typography-label-lg-font: normal var(--t-typography-label-font-weight) var(--t-typography-label-lg-font-size) /
    var(--t-typography-label-lg-line-height) var(--t-font-family-primary);

  --t-typography-label-md-font-size: var(--t-font-size-350);
  --t-typography-label-md-line-height: var(--t-line-height-500);
  --t-typography-label-md-font: normal var(--t-typography-label-font-weight) var(--t-typography-label-md-font-size) /
    var(--t-typography-label-md-line-height) var(--t-font-family-primary);

  --t-typography-label-sm-font-size: var(--t-font-size-300);
  --t-typography-label-sm-line-height: var(--t-line-height-400);
  --t-typography-label-sm-font: normal var(--t-typography-label-font-weight) var(--t-typography-label-sm-font-size) /
    var(--t-typography-label-sm-line-height) var(--t-font-family-primary);

  // SPACING
  --t-spacing-xs: 0.25rem;
  --t-spacing-sm: 0.5rem;
  --t-spacing-md: 1rem;
  --t-spacing-lg: 1.5rem;
  --t-spacing-xl: 2rem;
  --t-spacing-xxl: 3rem;
  --t-spacing-4xl: 4rem;

  // COLORS
  // Neutral Colors
  --t-color-neutral-white: #ffffff;
  --t-color-neutral-100: #fafafa;
  --t-color-neutral-200: #f5f5f5;
  --t-color-neutral-300: #e6e6e6;
  --t-color-neutral-400: #d7d7d9;
  --t-color-neutral-500: #c2c2c2;
  --t-color-neutral-600: #a4a5a6;
  --t-color-neutral-700: #6a6a6b;
  --t-color-neutral-800: #373838;
  --t-color-neutral-900: #252627;

  // Blue
  --t-color-blue-50: #eff6ff;
  --t-color-blue-100: #f0f5ff;
  --t-color-blue-200: #e0ecff;
  --t-color-blue-300: #bfdcfe;
  --t-color-blue-400: #7bb8fa;
  --t-color-blue-500: #2e7cf6;
  --t-color-blue-600: #2465ec;
  --t-color-blue-700: #1c50d9;

  // Red
  --t-color-red-100: #ffefed;
  --t-color-red-200: #fdcfcb;
  --t-color-red-300: #faaea7;
  --t-color-red-400: #f57e74;
  --t-color-red-500: #ea4335;
  --t-color-red-600: #d8382a;
  --t-color-red-700: #b62b1f;

  // Green
  --t-color-green-200: #e1faec;
  --t-color-green-300: #abefc6;
  --t-color-green-400: #4fe58b;
  --t-color-green-500: #00bd56;
  --t-color-green-600: #0eaa69;
  --t-color-green-700: #04784d;

  // Violet
  --t-color-violet-200: #eae5ff;
  --t-color-violet-500: #8c34fe;

  // Orange
  --t-color-orange-200: #fee9be;
  --t-color-orange-300: #ffd86d;
  --t-color-orange-400: #ffa800;
  --t-color-orange-500: #ff8c00;

  // Yellow
  --t-color-yellow-100: #fdf7d7;
  --t-color-yellow-200: #fdf5ca;
  --t-color-yellow-300: #ffe941;
  --t-color-yellow-400: #ffe40d;
  --t-color-yellow-500: #ffd500;
  --t-color-yellow-600: #d9b600;

  --t-text-color-primary: #252627;
  --t-text-color-secondary: #6a6a6b;
  --t-text-color-tertiary: #959696;
  --t-text-color-danger-primary: var(--t-color-red-500);
  --t-text-color-danger-secondary: var(--t-color-red-400);

  // Border radius
  --t-border-radius-none: 0;
  --t-border-radius-xs: 0.25rem;
  --t-border-radius-sm: 0.5rem;
  --t-border-radius-md: 0.75rem;
  --t-border-radius-lg: 1rem;
  --t-border-radius-half-rounded: 50%;
  --t-border-radius-full-rounded: 100%;

  --t-shadow-sm: 0px 0.125rem 1.25rem 0px rgba(31, 32, 33, 0.06);
  --t-shadow-md: 0px 0.25rem 1.875rem 0px rgba(31, 32, 33, 0.16);
  --t-form-field-focus-shadow: 0 0 0 0.25rem var(--t-color-neutral-200);

  --t-header-height: 5rem;
  --t-header-height-mobile: 3.75rem;
}
