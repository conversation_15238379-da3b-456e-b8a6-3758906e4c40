name: Deployment - tallo-fe
run-name: Deployment ${{ inputs.app }} to ${{ inputs.stage }}

on:
  workflow_call:
    inputs:
      app:
        type: string
        description: 'Frontend Application Name'
        required: true
        default: ''
      tag:
        type: string
        description: 'Tag/Commit/Branch Name'
        required: true
        default: 'main'
      stage:
        type: string
        required: true
        default: 'production'
      aws_region:
        type: string
        required: true
        default: 'us-east-1'

  workflow_dispatch:
    inputs:
      app:
        description: 'Frontend Application Name'
        required: true
        default: ''
      tag:
        description: 'Tag/Commit/Branch Name'
        required: true
        default: 'main'
      stage:
        description: 'Stage to deploy to'
        type: choice
        required: true
        options:
          - production
          - staging
      aws_region:
        description: 'AWS Region'
        required: true
        default: 'us-east-1'

concurrency:
  group: ${{ github.workflow }}-${{ inputs.stage }}-${{ inputs.app }}-${{ inputs.tag }}

env:
  GA_SERVICE: tallo-fe
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ${{ inputs.aws_region }}
  AWS_ACCOUNT_ID: ************
  STAGE: ${{ inputs.stage }}
  TAG: ${{ inputs.tag }}
  APP: ${{ inputs.app }}
  DIST_PATH: apps/${{ inputs.app }}/dist
  DATADOG_APP_VERSION: ${{ github.run_id }}_${{ github.run_attempt }}

jobs:
  map-app-stage:
    name: '${{ inputs.app }} ${{ inputs.stage }}: Deploy ${{ inputs.tag }}'
    if: |
      (contains(inputs.app, 'main-app') ||
      contains(inputs.app, 'renter-app') ||
      contains(inputs.app, 'docs')) &&
      ( inputs.app != '' || github.event.inputs.app != '' )
    runs-on: ubuntu-latest
    outputs:
      s3_bucket: ${{ env.s3_bucket }}
      cloudfront_id: ${{ env.cloudfront_id }}
      datadog_service_name: ${{ env.datadog_service_name }}

    steps:
      - name: test
        run: |
          cat <<EOF
          ${{ toJson(github) }}
          EOF

      - name: Map AWS resources to stage ${{ env.STAGE }}
        uses: kanga333/variable-mapper@master
        with:
          key: '${{ env.APP }}-${{ env.STAGE }}'
          map: |
            {
              "main-app-production": {
                "s3_bucket": "app.tallo.ai",
                "cloudfront_id": "E2WZTPFZZKLC8D",
                "datadog_service_name": "investor-app"
              },
              "main-app-staging": {
                "s3_bucket": "app-staging.tallo.ai",
                "cloudfront_id": "EYM7OFVF9VR6X",
                "datadog_service_name": "investor-app"
              },
              "renter-app-production": {
                "s3_bucket": "homebase.tallo.ai",
                "cloudfront_id": "EZM9GKRWK8CMH",
                "datadog_service_name": "renter-app"
              },
              "renter-app-staging": {
                "s3_bucket": "homebase-staging.tallo.ai",
                "cloudfront_id": "E3N6S8LX9MNC40",
                "datadog_service_name": "renter-app"
              },
              "docs-production": {
                "s3_bucket": "docs.tallo.ai",
                "cloudfront_id": "E1EP94I1ZO601A",
                "datadog_service_name": ""
              },
              ".*": {
                "s3_bucket": "",
                "cloudfront_id": "",
                "datadog_service_name": ""
              }
            }

  deploy:
    name: Deploy ${{ inputs.app }}:${{ inputs.tag }} to ${{ inputs.stage }}
    if: needs.map-app-stage.outputs.s3_bucket != '' && needs.map-app-stage.outputs.cloudfront_id != ''
    needs:
      - map-app-stage
    runs-on: ubuntu-latest
    environment: ${{ inputs.stage }}
    env:
      s3_bucket: ${{ needs.map-app-stage.outputs.s3_bucket }}
      cloudfront_id: ${{ needs.map-app-stage.outputs.cloudfront_id }}
      datadog_service_name: ${{ needs.map-app-stage.outputs.datadog_service_name }}

    steps:
      - name: Checkout FE code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ env.TAG }}
          token: ${{ secrets.GH_TOKEN }}

      - name: Install awscli
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o /tmp/awscliv2.zip
          unzip -q /tmp/awscliv2.zip -d /tmp
          rm /tmp/awscliv2.zip
          sudo /tmp/aws/install --update
          rm -rf /tmp/aws/

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Build project
        run: |
          npx nx run ${{ env.APP }}:build:${{ env.STAGE }} --verbose --skip-nx-cache

      - name: Deploy source maps to Datadog
        if: env.datadog_service_name != ''
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
        run: |
          npx datadog-ci sourcemaps upload ${{ env.DIST_PATH }} \
            --service=${{ env.datadog_service_name }} \
            --release-version=${{ env.DATADOG_APP_VERSION }} \
            --minified-path-prefix=https://${{ env.s3_bucket }}/

      - name: Delete source maps for production
        if: env.datadog_service_name != '' && github.event.inputs.stage == 'production'
        uses: ubie-oss/delete-source-map-action@v1
        with:
          directory: ${{ env.DIST_PATH }}

      - name: Deploy to s3
        run: aws s3 sync ${{ env.DIST_PATH }} s3://${{ env.s3_bucket }} --delete

      - name: Invalidate cloudfront cache tallo.io
        env:
          AWS_MAX_ATTEMPTS: 10
        run: aws cloudfront create-invalidation --distribution-id ${{ env.cloudfront_id }} --paths "/*"

      - name: Send failure to slack
        if: ${{ failure() }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '${{ vars.SLACK_CHANNEL }}'
          SLACK_MSG_AUTHOR: 'tallo'
          SLACK_COLOR: 'failure'
          SLACK_MESSAGE: "
            Stage: ${{ env.STAGE }}\n
            Commit SHA/Branches: \n
            - `${{ env.TAG }}`\n
            "
          SLACK_TITLE: '${{ env.APP }}: Failed to Deploy to ${{ env.STAGE }}'
          MSG_MINIMAL: 'actions url'
